@echo off

echo.
echo Checking Rust installation...
echo.

where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] rustc found:
    rustc --version
) else (
    echo [ERROR] rustc not found
    goto :install_help
)

where cargo >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] cargo found:
    cargo --version
) else (
    echo [ERROR] cargo not found
    goto :install_help
)

where rustup >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] rustup found:
    rustup --version
) else (
    echo [ERROR] rustup not found
    goto :install_help
)

echo.
echo SUCCESS: Rust is ready!
echo You can now run: build.bat
echo.
pause
exit /b 0

:install_help
echo.
echo To install Rust:
echo 1. Visit https://rustup.rs/
echo 2. Download rustup-init.exe
echo 3. Run the installer
echo 4. Restart command prompt
echo 5. Run this script again
echo.
pause
exit /b 1
