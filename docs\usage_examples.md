# COM2TCP 使用示例

## 基本使用

### 1. 查看帮助信息

```bash
com2tcp --help
```

### 2. 列出可用串口

```bash
com2tcp --list-ports
```

### 3. 基本启动（使用默认设置）

```bash
# Windows
com2tcp --port COM1 --baud 9600 --tcp-port 8080

# Linux
com2tcp --port /dev/ttyUSB0 --baud 9600 --tcp-port 8080
```

### 4. 完整参数示例

```bash
com2tcp \
  --port COM1 \
  --baud 115200 \
  --data-bits 8 \
  --stop-bits 1 \
  --parity none \
  --tcp-port 8080 \
  --tcp-host 0.0.0.0 \
  --max-clients 5 \
  --verbose
```

## 高级配置

### 1. 使用配置文件

首先生成默认配置文件：

```bash
com2tcp --generate-config config.toml
```

然后编辑配置文件并使用：

```bash
com2tcp --config config.toml
```

### 2. 自定义缓冲区和超时

```bash
com2tcp \
  --port COM1 \
  --baud 9600 \
  --tcp-port 8080 \
  --buffer-size 2048 \
  --client-timeout 600 \
  --timeout 2000
```

### 3. 日志配置

```bash
# 设置日志级别
com2tcp --port COM1 --log-level debug

# 输出日志到文件
com2tcp --port COM1 --log-file com2tcp.log

# 详细输出
com2tcp --port COM1 --verbose
```

## 客户端连接示例

### 1. 使用 telnet 连接

```bash
telnet localhost 8080
```

### 2. 使用 netcat 连接

```bash
# Linux/macOS
nc localhost 8080

# Windows (如果安装了 netcat)
nc.exe localhost 8080
```

### 3. 使用 Python 客户端

```python
import socket
import threading
import time

def receive_data(sock):
    while True:
        try:
            data = sock.recv(1024)
            if not data:
                break
            print(f"Received: {data.decode('utf-8', errors='ignore')}")
        except:
            break

# 连接到服务器
client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('localhost', 8080))

# 启动接收线程
receive_thread = threading.Thread(target=receive_data, args=(client,))
receive_thread.daemon = True
receive_thread.start()

# 发送数据
try:
    while True:
        message = input("Enter message: ")
        if message.lower() == 'quit':
            break
        client.send(message.encode('utf-8'))
        time.sleep(0.1)
finally:
    client.close()
```

### 4. 使用 C# 客户端

```csharp
using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class TcpClient
{
    static async Task Main(string[] args)
    {
        using var client = new TcpClient();
        await client.ConnectAsync("localhost", 8080);
        
        using var stream = client.GetStream();
        
        // 启动接收任务
        var receiveTask = Task.Run(async () =>
        {
            var buffer = new byte[1024];
            while (client.Connected)
            {
                try
                {
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead > 0)
                    {
                        string message = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        Console.WriteLine($"Received: {message}");
                    }
                }
                catch
                {
                    break;
                }
            }
        });
        
        // 发送数据
        while (client.Connected)
        {
            Console.Write("Enter message: ");
            string input = Console.ReadLine();
            if (input?.ToLower() == "quit")
                break;
                
            byte[] data = Encoding.UTF8.GetBytes(input + "\r\n");
            await stream.WriteAsync(data, 0, data.Length);
        }
    }
}
```

## 常见使用场景

### 1. 串口调试

```bash
# 启动服务器
com2tcp --port COM1 --baud 115200 --tcp-port 8080 --verbose

# 使用多个客户端同时监控串口数据
# 客户端1: telnet localhost 8080
# 客户端2: nc localhost 8080
# 客户端3: 自定义应用程序
```

### 2. 远程设备控制

```bash
# 在设备所在的机器上启动
com2tcp --port /dev/ttyUSB0 --baud 9600 --tcp-host 0.0.0.0 --tcp-port 8080

# 从远程机器连接
telnet device_ip 8080
```

### 3. 数据记录和分析

```bash
# 启动服务器并记录日志
com2tcp --port COM1 --baud 9600 --tcp-port 8080 --log-file data.log --log-level debug

# 使用客户端连接并保存数据
nc localhost 8080 | tee received_data.txt
```

### 4. 多设备管理

```bash
# 设备1
com2tcp --port COM1 --baud 9600 --tcp-port 8081

# 设备2
com2tcp --port COM2 --baud 115200 --tcp-port 8082

# 设备3
com2tcp --port COM3 --baud 38400 --tcp-port 8083
```

## 故障排除

### 1. 串口无法打开

```bash
# 检查可用串口
com2tcp --list-ports

# 确认串口未被其他程序占用
# Windows: 检查设备管理器
# Linux: lsof /dev/ttyUSB0
```

### 2. TCP端口被占用

```bash
# Windows: 查看端口占用
netstat -an | findstr :8080

# Linux: 查看端口占用
netstat -tulpn | grep :8080

# 使用其他端口
com2tcp --port COM1 --tcp-port 8081
```

### 3. 客户端连接问题

```bash
# 检查防火墙设置
# 确认服务器正在监听
netstat -an | findstr :8080

# 测试本地连接
telnet localhost 8080
```

### 4. 数据传输问题

```bash
# 启用详细日志
com2tcp --port COM1 --tcp-port 8080 --verbose --log-level debug

# 检查串口参数是否正确
com2tcp --port COM1 --baud 9600 --data-bits 8 --stop-bits 1 --parity none
```

## 性能优化

### 1. 调整缓冲区大小

```bash
# 对于高速数据传输
com2tcp --port COM1 --baud 115200 --buffer-size 4096 --max-queue-size 2000
```

### 2. 限制客户端数量

```bash
# 限制最大客户端数量以节省资源
com2tcp --port COM1 --max-clients 3
```

### 3. 调整超时设置

```bash
# 对于稳定连接，可以增加超时时间
com2tcp --port COM1 --client-timeout 1800 --timeout 5000
```

## 配置文件示例

```toml
[serial]
port = "COM1"
baud_rate = 115200
data_bits = 8
stop_bits = 1
parity = "None"
timeout = 1000

[tcp]
host = "0.0.0.0"
port = 8080
max_clients = 10
client_timeout = 300000
buffer_size = 1024

[logging]
level = "info"
file = "com2tcp.log"

[limits]
max_message_size = 1048576
max_queue_size = 1000
```
