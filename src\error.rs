use std::fmt;
use thiserror::Error;

/// Main error type for COM2TCP application
#[derive(Debug, Error)]
pub enum Com2TcpError {
    #[error("Serial port error: {0}")]
    Serial(#[from] SerialError),
    
    #[error("TCP server error: {0}")]
    Tcp(#[from] TcpError),
    
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Data forwarder error: {0}")]
    Forwarder(#[from] ForwarderError),
    
    #[error("Application error: {0}")]
    Application(String),
}

/// Serial port related errors
#[derive(Debug, Error)]
pub enum SerialError {
    #[error("Failed to open port '{port}': {source}")]
    OpenFailed {
        port: String,
        #[source]
        source: serialport::Error,
    },
    
    #[error("Port '{port}' is disconnected")]
    Disconnected { port: String },
    
    #[error("Read timeout on port '{port}'")]
    ReadTimeout { port: String },
    
    #[error("Write failed on port '{port}': {source}")]
    WriteFailed {
        port: String,
        #[source]
        source: serialport::Error,
    },
    
    #[error("Invalid port configuration: {message}")]
    InvalidConfig { message: String },
    
    #[error("Port '{port}' is not available")]
    PortNotAvailable { port: String },
    
    #[error("Serial port operation failed: {0}")]
    OperationFailed(#[from] serialport::Error),
}

/// TCP server related errors
#[derive(Debug, Error)]
pub enum TcpError {
    #[error("Failed to bind to {host}:{port}: {source}")]
    BindFailed {
        host: String,
        port: u16,
        #[source]
        source: std::io::Error,
    },
    
    #[error("Client connection failed: {source}")]
    ConnectionFailed {
        #[source]
        source: std::io::Error,
    },
    
    #[error("Client {client_id} disconnected unexpectedly")]
    ClientDisconnected { client_id: u64 },
    
    #[error("Failed to send data to client {client_id}: {source}")]
    SendFailed {
        client_id: u64,
        #[source]
        source: std::io::Error,
    },
    
    #[error("Failed to receive data from client {client_id}: {source}")]
    ReceiveFailed {
        client_id: u64,
        #[source]
        source: std::io::Error,
    },
    
    #[error("Maximum number of clients ({max}) exceeded")]
    MaxClientsExceeded { max: usize },
    
    #[error("Client {client_id} timeout")]
    ClientTimeout { client_id: u64 },
    
    #[error("Invalid TCP configuration: {message}")]
    InvalidConfig { message: String },
}

/// Data forwarder related errors
#[derive(Debug, Error)]
pub enum ForwarderError {
    #[error("Message queue is full (size: {size})")]
    QueueFull { size: usize },
    
    #[error("Failed to forward data from serial to TCP: {source}")]
    SerialToTcpFailed {
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },
    
    #[error("Failed to forward data from TCP to serial: {source}")]
    TcpToSerialFailed {
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },
    
    #[error("Event dispatcher error: {message}")]
    DispatcherError { message: String },
    
    #[error("Invalid message format: {message}")]
    InvalidMessage { message: String },
    
    #[error("Buffer overflow: attempted to write {attempted} bytes to buffer of size {capacity}")]
    BufferOverflow { attempted: usize, capacity: usize },
    
    #[error("Channel communication error: {message}")]
    ChannelError { message: String },
}

/// Result type alias for COM2TCP operations
pub type Result<T> = std::result::Result<T, Com2TcpError>;

/// Result type alias for serial operations
pub type SerialResult<T> = std::result::Result<T, SerialError>;

/// Result type alias for TCP operations
pub type TcpResult<T> = std::result::Result<T, TcpError>;

/// Result type alias for forwarder operations
pub type ForwarderResult<T> = std::result::Result<T, ForwarderError>;

impl SerialError {
    /// Create a new port not available error
    pub fn port_not_available(port: &str) -> Self {
        Self::PortNotAvailable {
            port: port.to_string(),
        }
    }
    
    /// Create a new invalid config error
    pub fn invalid_config(message: &str) -> Self {
        Self::InvalidConfig {
            message: message.to_string(),
        }
    }
    
    /// Create a new disconnected error
    pub fn disconnected(port: &str) -> Self {
        Self::Disconnected {
            port: port.to_string(),
        }
    }
}

impl TcpError {
    /// Create a new bind failed error
    pub fn bind_failed(host: &str, port: u16, source: std::io::Error) -> Self {
        Self::BindFailed {
            host: host.to_string(),
            port,
            source,
        }
    }
    
    /// Create a new max clients exceeded error
    pub fn max_clients_exceeded(max: usize) -> Self {
        Self::MaxClientsExceeded { max }
    }
    
    /// Create a new client timeout error
    pub fn client_timeout(client_id: u64) -> Self {
        Self::ClientTimeout { client_id }
    }
    
    /// Create a new invalid config error
    pub fn invalid_config(message: &str) -> Self {
        Self::InvalidConfig {
            message: message.to_string(),
        }
    }
}

impl ForwarderError {
    /// Create a new queue full error
    pub fn queue_full(size: usize) -> Self {
        Self::QueueFull { size }
    }
    
    /// Create a new dispatcher error
    pub fn dispatcher_error(message: &str) -> Self {
        Self::DispatcherError {
            message: message.to_string(),
        }
    }
    
    /// Create a new invalid message error
    pub fn invalid_message(message: &str) -> Self {
        Self::InvalidMessage {
            message: message.to_string(),
        }
    }
    
    /// Create a new buffer overflow error
    pub fn buffer_overflow(attempted: usize, capacity: usize) -> Self {
        Self::BufferOverflow { attempted, capacity }
    }
    
    /// Create a new channel error
    pub fn channel_error(message: &str) -> Self {
        Self::ChannelError {
            message: message.to_string(),
        }
    }
}

impl Com2TcpError {
    /// Create a new configuration error
    pub fn config_error(message: &str) -> Self {
        Self::Config(message.to_string())
    }
    
    /// Create a new application error
    pub fn application_error(message: &str) -> Self {
        Self::Application(message.to_string())
    }
}

/// Helper trait for converting errors to COM2TCP errors
pub trait IntoComError<T> {
    fn into_com_error(self) -> Result<T>;
}

impl<T, E> IntoComError<T> for std::result::Result<T, E>
where
    E: Into<Com2TcpError>,
{
    fn into_com_error(self) -> Result<T> {
        self.map_err(|e| e.into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_serial_error_creation() {
        let error = SerialError::port_not_available("COM1");
        assert!(matches!(error, SerialError::PortNotAvailable { .. }));
        assert!(error.to_string().contains("COM1"));
    }
    
    #[test]
    fn test_tcp_error_creation() {
        let error = TcpError::max_clients_exceeded(10);
        assert!(matches!(error, TcpError::MaxClientsExceeded { max: 10 }));
        assert!(error.to_string().contains("10"));
    }
    
    #[test]
    fn test_forwarder_error_creation() {
        let error = ForwarderError::queue_full(100);
        assert!(matches!(error, ForwarderError::QueueFull { size: 100 }));
        assert!(error.to_string().contains("100"));
    }
    
    #[test]
    fn test_error_conversion() {
        let serial_error = SerialError::port_not_available("COM1");
        let com_error: Com2TcpError = serial_error.into();
        assert!(matches!(com_error, Com2TcpError::Serial(_)));
    }
}
