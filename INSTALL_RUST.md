# Rust 安装指南

## Windows 系统安装 Rust

### 方法1：使用 rustup（推荐）

1. **下载 rustup-init.exe**
   - 访问 https://rustup.rs/
   - 点击 "Download rustup-init.exe (64-bit)" 下载安装程序

2. **运行安装程序**
   - 双击下载的 `rustup-init.exe`
   - 选择 "1) Proceed with installation (default)"
   - 等待安装完成

3. **重启命令提示符**
   - 关闭当前的 PowerShell 或命令提示符窗口
   - 重新打开一个新的窗口

4. **验证安装**
   ```cmd
   rustc --version
   cargo --version
   ```

### 方法2：使用 Chocolatey（如果已安装）

```cmd
choco install rust
```

### 方法3：使用 Scoop（如果已安装）

```cmd
scoop install rust
```

## 安装后配置

### 1. 更新 Rust（可选）
```cmd
rustup update
```

### 2. 安装常用组件
```cmd
rustup component add clippy rustfmt
```

### 3. 设置国内镜像（可选，加速下载）

创建或编辑文件 `%USERPROFILE%\.cargo\config.toml`：

```toml
[source.crates-io]
replace-with = 'ustc'

[source.ustc]
registry = "git://mirrors.ustc.edu.cn/crates.io-index"

[http]
check-revoke = false
```

## 验证安装

运行以下命令确认安装成功：

```cmd
rustc --version
cargo --version
rustup --version
```

应该看到类似输出：
```
rustc 1.75.0 (82e1608df 2023-12-21)
cargo 1.75.0 (1d8b05cdd 2023-11-20)
rustup 1.26.0 (5af9b9484 2023-04-05)
```

## 常见问题

### 问题1：命令找不到
**症状**：运行 `cargo` 命令提示找不到
**解决**：
1. 重启命令提示符
2. 检查环境变量是否正确设置
3. 手动添加 `%USERPROFILE%\.cargo\bin` 到 PATH

### 问题2：网络连接问题
**症状**：下载缓慢或失败
**解决**：
1. 使用国内镜像（见上面配置）
2. 检查防火墙设置
3. 使用代理（如果需要）

### 问题3：权限问题
**症状**：安装时提示权限不足
**解决**：
1. 以管理员身份运行安装程序
2. 或者安装到用户目录（默认行为）

## 卸载 Rust

如果需要卸载：

```cmd
rustup self uninstall
```

## 下一步

安装完成后，您可以：

1. 运行 `check_rust.bat` 验证环境
2. 运行 `build.bat` 编译 COM2TCP 项目
3. 查看 `docs/user_manual.md` 了解使用方法

## 更多资源

- [Rust 官方网站](https://www.rust-lang.org/)
- [Rust 中文文档](https://rustwiki.org/)
- [Cargo 文档](https://doc.rust-lang.org/cargo/)
- [Rustup 文档](https://rust-lang.github.io/rustup/)
