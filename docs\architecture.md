# COM2TCP 架构设计文档

## 1. 系统架构概览

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    COM2TCP Application                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │ CLI Module  │  │ Config Mgr   │  │   Logger Module     │ │
│  └─────────────┘  └──────────────┘  └─────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Data Forwarder (Core)                     │ │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │ │
│  │  │  Message Queue  │    │    Event Dispatcher        │ │ │
│  │  └─────────────────┘    └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ Serial Manager  │              │    TCP Server Module   │ │
│  │                 │              │                         │ │
│  │ ┌─────────────┐ │              │ ┌─────────────────────┐ │ │
│  │ │Serial Device│ │              │ │  Client Manager     │ │ │
│  │ └─────────────┘ │              │ └─────────────────────┘ │ │
│  │ ┌─────────────┐ │              │ ┌─────────────────────┐ │ │
│  │ │Buffer Mgr   │ │              │ │  Connection Pool    │ │ │
│  │ └─────────────┘ │              │ └─────────────────────┘ │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
           │                                    │
           ▼                                    ▼
    ┌─────────────┐                    ┌─────────────────┐
    │Serial Device│                    │  TCP Clients    │
    │  (Hardware) │                    │ ┌─────────────┐ │
    └─────────────┘                    │ │   Client1   │ │
                                       │ └─────────────┘ │
                                       │ ┌─────────────┐ │
                                       │ │   Client2   │ │
                                       │ └─────────────┘ │
                                       │ ┌─────────────┐ │
                                       │ │   Client N  │ │
                                       │ └─────────────┘ │
                                       └─────────────────┘
```

### 1.2 数据流图

```
Serial Device ←→ Serial Manager ←→ Data Forwarder ←→ TCP Server ←→ TCP Clients
                                         ↕
                                   Message Queue
                                         ↕
                                  Event Dispatcher
```

## 2. 模块设计

### 2.1 主程序模块 (main.rs)

**职责**: 程序入口，初始化各个模块，启动主事件循环

```rust
pub struct Application {
    config: Config,
    serial_manager: SerialManager,
    tcp_server: TcpServer,
    data_forwarder: DataForwarder,
}

impl Application {
    pub async fn new(config: Config) -> Result<Self>;
    pub async fn run(&mut self) -> Result<()>;
    pub async fn shutdown(&mut self) -> Result<()>;
}
```

### 2.2 命令行接口模块 (cli.rs)

**职责**: 解析命令行参数，生成配置对象

```rust
#[derive(Parser)]
pub struct Args {
    #[arg(short, long, default_value = "COM1")]
    pub port: String,
    
    #[arg(short, long, default_value = "9600")]
    pub baud: u32,
    
    #[arg(short = 't', long, default_value = "8080")]
    pub tcp_port: u16,
    
    // ... 其他参数
}

pub fn parse_args() -> Args;
pub fn validate_args(args: &Args) -> Result<()>;
```

### 2.3 配置管理模块 (config.rs)

**职责**: 管理应用程序配置，支持多种配置源

```rust
#[derive(Debug, Clone)]
pub struct Config {
    pub serial: SerialConfig,
    pub tcp: TcpConfig,
    pub logging: LogConfig,
    pub limits: LimitsConfig,
}

#[derive(Debug, Clone)]
pub struct SerialConfig {
    pub port: String,
    pub baud_rate: u32,
    pub data_bits: DataBits,
    pub stop_bits: StopBits,
    pub parity: Parity,
    pub timeout: Duration,
}

#[derive(Debug, Clone)]
pub struct TcpConfig {
    pub host: String,
    pub port: u16,
    pub max_clients: usize,
    pub client_timeout: Duration,
    pub buffer_size: usize,
}
```

### 2.4 串口管理模块 (serial_manager.rs)

**职责**: 管理串口连接，处理串口数据读写

```rust
pub struct SerialManager {
    config: SerialConfig,
    port: Option<Box<dyn SerialPort>>,
    tx: mpsc::Sender<SerialEvent>,
    rx: mpsc::Receiver<SerialCommand>,
}

#[derive(Debug)]
pub enum SerialEvent {
    Connected,
    Disconnected,
    DataReceived(Vec<u8>),
    Error(SerialError),
}

#[derive(Debug)]
pub enum SerialCommand {
    Connect,
    Disconnect,
    SendData(Vec<u8>),
    Shutdown,
}

impl SerialManager {
    pub fn new(config: SerialConfig) -> (Self, SerialHandle);
    pub async fn run(&mut self) -> Result<()>;
    async fn connect(&mut self) -> Result<()>;
    async fn disconnect(&mut self) -> Result<()>;
    async fn read_data(&mut self) -> Result<Vec<u8>>;
    async fn write_data(&mut self, data: &[u8]) -> Result<()>;
}

pub struct SerialHandle {
    tx: mpsc::Sender<SerialCommand>,
    rx: mpsc::Receiver<SerialEvent>,
}
```

### 2.5 TCP服务器模块 (tcp_server.rs)

**职责**: 管理TCP服务器和客户端连接

```rust
pub struct TcpServer {
    config: TcpConfig,
    listener: Option<TcpListener>,
    clients: HashMap<ClientId, ClientConnection>,
    tx: mpsc::Sender<TcpEvent>,
    rx: mpsc::Receiver<TcpCommand>,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct ClientId(u64);

pub struct ClientConnection {
    id: ClientId,
    stream: TcpStream,
    addr: SocketAddr,
    connected_at: Instant,
    last_activity: Instant,
}

#[derive(Debug)]
pub enum TcpEvent {
    ClientConnected(ClientId, SocketAddr),
    ClientDisconnected(ClientId),
    DataReceived(ClientId, Vec<u8>),
    Error(ClientId, TcpError),
}

#[derive(Debug)]
pub enum TcpCommand {
    Start,
    Stop,
    SendToClient(ClientId, Vec<u8>),
    SendToAll(Vec<u8>),
    DisconnectClient(ClientId),
    Shutdown,
}

impl TcpServer {
    pub fn new(config: TcpConfig) -> (Self, TcpHandle);
    pub async fn run(&mut self) -> Result<()>;
    async fn accept_connections(&mut self) -> Result<()>;
    async fn handle_client(&mut self, client_id: ClientId) -> Result<()>;
    async fn broadcast_data(&mut self, data: &[u8]) -> Result<()>;
}

pub struct TcpHandle {
    tx: mpsc::Sender<TcpCommand>,
    rx: mpsc::Receiver<TcpEvent>,
}
```

### 2.6 数据转发模块 (data_forwarder.rs)

**职责**: 核心数据转发逻辑，协调串口和TCP之间的数据流

```rust
pub struct DataForwarder {
    serial_handle: SerialHandle,
    tcp_handle: TcpHandle,
    message_queue: MessageQueue,
    event_dispatcher: EventDispatcher,
    statistics: Statistics,
}

#[derive(Debug)]
pub enum ForwarderEvent {
    SerialData(Vec<u8>),
    TcpData(ClientId, Vec<u8>),
    ClientConnected(ClientId),
    ClientDisconnected(ClientId),
    SerialConnected,
    SerialDisconnected,
    Error(ForwarderError),
}

pub struct MessageQueue {
    serial_to_tcp: VecDeque<Vec<u8>>,
    tcp_to_serial: VecDeque<(ClientId, Vec<u8>)>,
    max_size: usize,
}

pub struct EventDispatcher {
    handlers: HashMap<String, Box<dyn EventHandler>>,
}

pub struct Statistics {
    bytes_serial_to_tcp: AtomicU64,
    bytes_tcp_to_serial: AtomicU64,
    client_count: AtomicUsize,
    errors: AtomicU64,
    start_time: Instant,
}

impl DataForwarder {
    pub fn new(serial_handle: SerialHandle, tcp_handle: TcpHandle) -> Self;
    pub async fn run(&mut self) -> Result<()>;
    async fn handle_serial_event(&mut self, event: SerialEvent) -> Result<()>;
    async fn handle_tcp_event(&mut self, event: TcpEvent) -> Result<()>;
    async fn forward_serial_to_tcp(&mut self, data: Vec<u8>) -> Result<()>;
    async fn forward_tcp_to_serial(&mut self, client_id: ClientId, data: Vec<u8>) -> Result<()>;
    pub fn get_statistics(&self) -> Statistics;
}
```

## 3. 错误处理设计

### 3.1 错误类型定义

```rust
#[derive(Debug, thiserror::Error)]
pub enum Com2TcpError {
    #[error("Serial port error: {0}")]
    Serial(#[from] SerialError),
    
    #[error("TCP server error: {0}")]
    Tcp(#[from] TcpError),
    
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Forwarder error: {0}")]
    Forwarder(#[from] ForwarderError),
}

#[derive(Debug, thiserror::Error)]
pub enum SerialError {
    #[error("Failed to open port {port}: {source}")]
    OpenFailed { port: String, source: serialport::Error },
    
    #[error("Port disconnected")]
    Disconnected,
    
    #[error("Read timeout")]
    ReadTimeout,
    
    #[error("Write failed: {0}")]
    WriteFailed(serialport::Error),
}
```

### 3.2 错误恢复策略

- **串口错误**: 自动重连机制，指数退避
- **TCP错误**: 客户端断开时清理资源，服务器错误时重启监听
- **数据转发错误**: 记录错误，继续处理其他数据
- **配置错误**: 程序启动时验证，错误时退出

## 4. 并发模型

### 4.1 异步架构

使用tokio异步运行时，主要组件：
- 主事件循环：协调各个模块
- 串口读写任务：独立的异步任务
- TCP服务器任务：处理客户端连接
- 数据转发任务：处理数据流转

### 4.2 通信机制

- **模块间通信**: 使用mpsc channel
- **数据共享**: 使用Arc<Mutex<T>>或Arc<RwLock<T>>
- **事件通知**: 使用tokio::sync::notify

### 4.3 资源管理

- **连接池**: 管理TCP客户端连接
- **缓冲区**: 固定大小的环形缓冲区
- **内存管理**: 及时释放不用的资源

## 5. 性能优化

### 5.1 数据处理优化

- 零拷贝数据传输
- 批量数据处理
- 异步IO操作
- 缓冲区复用

### 5.2 并发优化

- 无锁数据结构
- 读写锁优化
- 任务调度优化
- CPU亲和性设置

### 5.3 内存优化

- 对象池模式
- 预分配缓冲区
- 内存映射文件
- 垃圾回收优化

## 6. 扩展性设计

### 6.1 插件系统

```rust
pub trait Plugin: Send + Sync {
    fn name(&self) -> &str;
    fn version(&self) -> &str;
    fn init(&mut self, config: &Config) -> Result<()>;
    fn on_data(&mut self, data: &[u8]) -> Result<Vec<u8>>;
    fn shutdown(&mut self) -> Result<()>;
}

pub struct PluginManager {
    plugins: Vec<Box<dyn Plugin>>,
}
```

### 6.2 协议扩展

```rust
pub trait Protocol: Send + Sync {
    fn encode(&self, data: &[u8]) -> Result<Vec<u8>>;
    fn decode(&self, data: &[u8]) -> Result<Vec<u8>>;
    fn validate(&self, data: &[u8]) -> bool;
}
```

### 6.3 配置扩展

支持多种配置格式：
- TOML配置文件
- JSON配置文件
- 环境变量
- 命令行参数

## 7. 安全考虑

### 7.1 访问控制

- IP白名单/黑名单
- 客户端认证
- 连接数限制
- 速率限制

### 7.2 数据安全

- 数据加密传输（可选）
- 敏感信息过滤
- 日志脱敏
- 安全审计

## 8. 监控和诊断

### 8.1 指标收集

- 连接数统计
- 数据传输量
- 错误率统计
- 性能指标

### 8.2 健康检查

- 串口连接状态
- TCP服务器状态
- 内存使用情况
- CPU使用率

### 8.3 日志系统

- 结构化日志
- 日志级别控制
- 日志轮转
- 远程日志收集
