use com2tcp::config::{Config, SerialConfig, TcpConfig, LogConfig, LimitsConfig};
use com2tcp::data_forwarder::DataForwarder;
use com2tcp::serial_manager::SerialManager;
use com2tcp::tcp_server::TcpServer;
use std::time::Duration;
use tokio::time::timeout;

/// Create a test configuration for integration tests
fn create_test_config() -> Config {
    Config {
        serial: SerialConfig {
            port: "COM99".to_string(), // Non-existent port for testing
            baud_rate: 9600,
            data_bits: serialport::DataBits::Eight,
            stop_bits: serialport::StopBits::One,
            parity: serialport::Parity::None,
            timeout: Duration::from_millis(100),
        },
        tcp: TcpConfig {
            host: "127.0.0.1".to_string(),
            port: 0, // Let OS choose port
            max_clients: 5,
            client_timeout: Duration::from_secs(30),
            buffer_size: 1024,
        },
        logging: LogConfig {
            level: log::LevelFilter::Debug,
            file: None,
        },
        limits: LimitsConfig {
            max_message_size: 1024 * 1024,
            max_queue_size: 1000,
        },
    }
}

#[tokio::test]
async fn test_application_components_creation() {
    let config = create_test_config();
    
    // Test serial manager creation
    let (serial_manager, _serial_handle) = SerialManager::new(config.serial.clone());
    assert_eq!(serial_manager.config.port, config.serial.port);
    
    // Test TCP server creation
    let (tcp_server, _tcp_handle) = TcpServer::new(config.tcp.clone());
    assert_eq!(tcp_server.config.host, config.tcp.host);
    
    // Test data forwarder creation
    let (_serial_manager2, serial_handle2) = SerialManager::new(config.serial.clone());
    let (_tcp_server2, tcp_handle2) = TcpServer::new(config.tcp.clone());
    let data_forwarder = DataForwarder::new(serial_handle2, tcp_handle2);
    
    let stats = data_forwarder.get_statistics().await;
    assert_eq!(stats.bytes_serial_to_tcp, 0);
    assert_eq!(stats.bytes_tcp_to_serial, 0);
}

#[tokio::test]
async fn test_tcp_server_lifecycle() {
    let config = create_test_config();
    let (mut tcp_server, mut tcp_handle) = TcpServer::new(config.tcp);
    
    // Start TCP server in background
    let server_task = tokio::spawn(async move {
        // Run server for a short time
        timeout(Duration::from_millis(500), tcp_server.run()).await
    });
    
    // Give server time to start
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Test server commands
    assert!(tcp_handle.start().await.is_ok());
    assert!(tcp_handle.get_status().await.is_ok());
    assert!(tcp_handle.shutdown().await.is_ok());
    
    // Wait for server to finish
    let _ = server_task.await;
}

#[tokio::test]
async fn test_serial_manager_with_invalid_port() {
    let config = create_test_config();
    let (mut serial_manager, mut serial_handle) = SerialManager::new(config.serial);
    
    // Start serial manager in background
    let manager_task = tokio::spawn(async move {
        // Run manager for a short time
        timeout(Duration::from_millis(500), serial_manager.run()).await
    });
    
    // Give manager time to start
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Test connection to invalid port (should fail)
    assert!(serial_handle.connect().await.is_ok()); // Command sent successfully
    
    // Check for error events
    if let Some(event) = serial_handle.recv_event().await {
        match event {
            com2tcp::serial_manager::SerialEvent::Error { .. } => {
                // Expected error for invalid port
            }
            _ => {
                // Other events are also acceptable
            }
        }
    }
    
    assert!(serial_handle.shutdown().await.is_ok());
    
    // Wait for manager to finish
    let _ = manager_task.await;
}

#[tokio::test]
async fn test_data_forwarder_statistics() {
    let config = create_test_config();
    let (_serial_manager, serial_handle) = SerialManager::new(config.serial);
    let (_tcp_server, tcp_handle) = TcpServer::new(config.tcp);
    
    let data_forwarder = DataForwarder::new(serial_handle, tcp_handle);
    
    // Test initial statistics
    let stats = data_forwarder.get_statistics().await;
    assert_eq!(stats.bytes_serial_to_tcp, 0);
    assert_eq!(stats.bytes_tcp_to_serial, 0);
    assert_eq!(stats.messages_serial_to_tcp, 0);
    assert_eq!(stats.messages_tcp_to_serial, 0);
    assert_eq!(stats.active_clients, 0);
    assert_eq!(stats.errors, 0);
    
    // Statistics should have reasonable timestamps
    assert!(stats.start_time.elapsed() < Duration::from_secs(1));
    assert!(stats.last_activity.elapsed() < Duration::from_secs(1));
}

#[tokio::test]
async fn test_config_validation() {
    let mut config = create_test_config();
    
    // Valid config should pass validation
    assert!(config.validate().is_ok());
    
    // Invalid serial port
    config.serial.port = String::new();
    assert!(config.validate().is_err());
    
    // Reset and test invalid baud rate
    config = create_test_config();
    config.serial.baud_rate = 0;
    assert!(config.validate().is_err());
    
    // Reset and test invalid TCP port
    config = create_test_config();
    config.tcp.port = 0; // This should be OK (OS chooses port)
    assert!(config.validate().is_ok());
    
    // Test invalid max clients
    config.tcp.max_clients = 0;
    assert!(config.validate().is_err());
    
    // Reset and test invalid buffer size
    config = create_test_config();
    config.tcp.buffer_size = 0;
    assert!(config.validate().is_err());
}

#[tokio::test]
async fn test_message_queue_functionality() {
    use com2tcp::data_forwarder::MessageQueue;
    use com2tcp::tcp_server::ClientId;
    
    let mut queue = MessageQueue::new(3);
    
    // Test serial to TCP queue
    assert!(queue.push_serial_to_tcp(vec![1, 2, 3]).is_ok());
    assert!(queue.push_serial_to_tcp(vec![4, 5, 6]).is_ok());
    assert!(queue.push_serial_to_tcp(vec![7, 8, 9]).is_ok());
    
    // Queue should be full now
    assert!(queue.push_serial_to_tcp(vec![10, 11, 12]).is_err());
    
    // Test popping items
    assert_eq!(queue.pop_serial_to_tcp(), Some(vec![1, 2, 3]));
    assert_eq!(queue.pop_serial_to_tcp(), Some(vec![4, 5, 6]));
    assert_eq!(queue.pop_serial_to_tcp(), Some(vec![7, 8, 9]));
    assert_eq!(queue.pop_serial_to_tcp(), None);
    
    // Test TCP to serial queue
    let client_id = ClientId::new();
    assert!(queue.push_tcp_to_serial(client_id, vec![1, 2, 3]).is_ok());
    assert_eq!(queue.pop_tcp_to_serial(), Some((client_id, vec![1, 2, 3])));
    assert_eq!(queue.pop_tcp_to_serial(), None);
    
    // Test queue length reporting
    assert!(queue.is_empty());
    let (serial_len, tcp_len) = queue.len();
    assert_eq!(serial_len, 0);
    assert_eq!(tcp_len, 0);
}

#[tokio::test]
async fn test_event_dispatcher() {
    use com2tcp::data_forwarder::{EventDispatcher, LoggingEventHandler};
    
    let mut dispatcher = EventDispatcher::new();
    
    // Register a handler
    let handler = Box::new(LoggingEventHandler);
    dispatcher.register_handler("test_event".to_string(), handler);
    
    // Dispatch an event
    let result = dispatcher.dispatch("test_event", b"test data").await;
    assert!(result.is_ok());
    
    // Dispatch to non-existent handler (should not fail)
    let result = dispatcher.dispatch("unknown_event", b"test data").await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_client_id_uniqueness() {
    use com2tcp::tcp_server::ClientId;
    use std::collections::HashSet;
    
    let mut ids = HashSet::new();
    
    // Generate multiple client IDs and ensure they're unique
    for _ in 0..1000 {
        let id = ClientId::new();
        assert!(ids.insert(id), "Duplicate client ID generated: {}", id);
    }
}

#[tokio::test]
async fn test_error_types() {
    use com2tcp::error::{Com2TcpError, SerialError, TcpError, ForwarderError};
    
    // Test error creation and conversion
    let serial_error = SerialError::port_not_available("COM1");
    let com_error: Com2TcpError = serial_error.into();
    assert!(matches!(com_error, Com2TcpError::Serial(_)));
    
    let tcp_error = TcpError::max_clients_exceeded(10);
    let com_error: Com2TcpError = tcp_error.into();
    assert!(matches!(com_error, Com2TcpError::Tcp(_)));
    
    let forwarder_error = ForwarderError::queue_full(100);
    let com_error: Com2TcpError = forwarder_error.into();
    assert!(matches!(com_error, Com2TcpError::Forwarder(_)));
    
    // Test error messages contain relevant information
    let error = SerialError::port_not_available("COM1");
    let error_string = error.to_string();
    assert!(error_string.contains("COM1"));
    
    let error = TcpError::max_clients_exceeded(5);
    let error_string = error.to_string();
    assert!(error_string.contains("5"));
}

// Helper function for testing with actual TCP connections
async fn create_test_tcp_client(port: u16) -> Result<tokio::net::TcpStream, std::io::Error> {
    tokio::net::TcpStream::connect(format!("127.0.0.1:{}", port)).await
}

#[tokio::test]
async fn test_tcp_server_with_real_connections() {
    let mut config = create_test_config();
    config.tcp.port = 0; // Let OS choose port
    
    let (mut tcp_server, mut tcp_handle) = TcpServer::new(config.tcp);
    
    // Start server
    let server_task = tokio::spawn(async move {
        timeout(Duration::from_secs(2), tcp_server.run()).await
    });
    
    // Give server time to start
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Start the server
    assert!(tcp_handle.start().await.is_ok());
    
    // Give server time to bind
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Note: In a real test, we would need to get the actual port the server bound to
    // For now, we'll just test the command interface
    
    assert!(tcp_handle.get_status().await.is_ok());
    assert!(tcp_handle.shutdown().await.is_ok());
    
    // Wait for server to finish
    let _ = server_task.await;
}
