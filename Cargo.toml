[package]
name = "com2tcp"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A bidirectional serial port to TCP forwarder supporting multiple concurrent clients"
license = "MIT"
repository = "https://github.com/yourusername/com2tcp"
keywords = ["serial", "tcp", "forwarder", "com", "bridge"]
categories = ["command-line-utilities", "network-programming"]

[dependencies]
# Async runtime
tokio = { version = "1.35", features = ["full"] }

# Serial port communication
serialport = "4.2"

# Command line argument parsing
clap = { version = "4.4", features = ["derive"] }

# Logging
log = "0.4"
env_logger = "0.10"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Serialization
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"

# Utilities
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.8"
serial-test = "3.0"
criterion = { version = "0.5", features = ["html_reports"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

[[bin]]
name = "com2tcp"
path = "src/main.rs"

[[bench]]
name = "performance_tests"
harness = false

[features]
default = []
# Enable TLS support for TCP connections
tls = ["tokio-rustls"]
# Enable web interface
web = ["axum", "tower", "tower-http"]
# Enable metrics collection
metrics = ["prometheus"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]
