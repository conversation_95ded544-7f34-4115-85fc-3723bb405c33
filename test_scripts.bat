@echo off

echo.
echo Testing all batch scripts...
echo ============================
echo.

echo Testing simple_check.bat...
if exist "simple_check.bat" (
    echo [OK] simple_check.bat exists
) else (
    echo [ERROR] simple_check.bat not found
)

echo Testing simple_build.bat...
if exist "simple_build.bat" (
    echo [OK] simple_build.bat exists
) else (
    echo [ERROR] simple_build.bat not found
)

echo Testing install_rust.bat...
if exist "install_rust.bat" (
    echo [OK] install_rust.bat exists
) else (
    echo [ERROR] install_rust.bat not found
)

echo Testing quick_start.bat...
if exist "quick_start.bat" (
    echo [OK] quick_start.bat exists
) else (
    echo [ERROR] quick_start.bat not found
)

echo.
echo All scripts found!
echo.
echo Next steps:
echo 1. Run simple_check.bat to check Rust
echo 2. If Rust not installed, run install_rust.bat
echo 3. Run simple_build.bat to compile
echo 4. Run quick_start.bat to use the program
echo.

pause
