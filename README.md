# COM2TCP - 串口到TCP转发器

## 项目概述

COM2TCP是一个串口到TCP的双向数据转发器，允许多个TCP客户端同时连接到一个串口设备，实现数据的双向透明传输。

## 功能特性

- 支持指定串口设备的连接和管理
- 创建TCP服务器，支持多客户端并发连接
- 串口数据实时转发到所有连接的TCP客户端
- TCP客户端数据转发到串口设备
- 客户端之间数据同步显示
- 命令行配置和控制
- 连接状态监控和日志记录

## 使用场景

- 串口设备的远程访问
- 多用户同时监控串口数据
- 串口设备的网络化改造
- 调试和测试工具

## 技术要求

- 编程语言：Rust
- 支持平台：Windows, Linux, macOS
- 依赖库：tokio, serialport, clap, log

## 快速开始

```bash
# 基本使用
com2tcp --port COM1 --baud 9600 --tcp-port 8080

# 查看帮助
com2tcp --help
```

## 项目结构

```
com2tcp/
├── src/
│   ├── main.rs              # 程序入口
│   ├── cli.rs               # 命令行参数解析
│   ├── serial_manager.rs    # 串口管理模块
│   ├── tcp_server.rs        # TCP服务器模块
│   ├── data_forwarder.rs    # 数据转发逻辑
│   └── config.rs            # 配置管理
├── tests/                   # 测试文件
├── docs/                    # 文档
├── Cargo.toml              # 项目配置
└── README.md               # 项目说明
```

## 开发状态

项目正在开发中，当前版本：v0.1.0-dev

## 许可证

MIT License
