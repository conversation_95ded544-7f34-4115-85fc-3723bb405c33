# COM2TCP API 参考文档

## 概述

COM2TCP 提供了一套完整的 API 来管理串口到 TCP 的数据转发。本文档描述了所有公共 API 的使用方法。

## 核心模块

### 1. Config 模块

#### Config 结构体

主要的配置结构体，包含所有应用程序设置。

```rust
pub struct Config {
    pub serial: SerialConfig,
    pub tcp: TcpConfig,
    pub logging: LogConfig,
    pub limits: LimitsConfig,
}
```

**方法：**

- `Config::from_args(args: Args) -> Result<Self>` - 从命令行参数创建配置
- `Config::from_file(path: &str) -> Result<Self>` - 从 TOML 文件加载配置
- `Config::save_to_file(&self, path: &str) -> Result<()>` - 保存配置到文件
- `Config::validate(&self) -> Result<()>` - 验证配置有效性
- `Config::default() -> Self` - 创建默认配置

#### SerialConfig 结构体

串口配置参数。

```rust
pub struct SerialConfig {
    pub port: String,
    pub baud_rate: u32,
    pub data_bits: serialport::DataBits,
    pub stop_bits: serialport::StopBits,
    pub parity: serialport::Parity,
    pub timeout: Duration,
}
```

#### TcpConfig 结构体

TCP 服务器配置参数。

```rust
pub struct TcpConfig {
    pub host: String,
    pub port: u16,
    pub max_clients: usize,
    pub client_timeout: Duration,
    pub buffer_size: usize,
}
```

### 2. SerialManager 模块

#### SerialManager 结构体

管理串口连接和数据传输。

```rust
pub struct SerialManager {
    pub config: SerialConfig,
    // 私有字段...
}
```

**方法：**

- `SerialManager::new(config: SerialConfig) -> (Self, SerialHandle)` - 创建串口管理器和句柄
- `run(&mut self) -> SerialResult<()>` - 运行串口管理器主循环

#### SerialHandle 结构体

用于与串口管理器通信的句柄。

**方法：**

- `connect(&self) -> SerialResult<()>` - 连接到串口
- `disconnect(&self) -> SerialResult<()>` - 断开串口连接
- `send_data(&self, data: Vec<u8>) -> SerialResult<()>` - 发送数据到串口
- `recv_event(&mut self) -> Option<SerialEvent>` - 接收串口事件
- `get_status(&self) -> SerialResult<()>` - 请求状态更新
- `shutdown(&self) -> SerialResult<()>` - 关闭串口管理器

#### SerialEvent 枚举

串口事件类型。

```rust
pub enum SerialEvent {
    Connected { port_name: String, config: SerialConfig },
    Disconnected { port_name: String, reason: String },
    DataReceived { data: Vec<u8>, timestamp: Instant },
    Error { error: SerialError, timestamp: Instant },
    StatusUpdate { connected: bool, bytes_sent: u64, bytes_received: u64, last_activity: Instant },
}
```

#### SerialCommand 枚举

可发送给串口管理器的命令。

```rust
pub enum SerialCommand {
    Connect,
    Disconnect,
    SendData { data: Vec<u8> },
    GetStatus,
    Shutdown,
}
```

### 3. TcpServer 模块

#### TcpServer 结构体

管理 TCP 服务器和客户端连接。

```rust
pub struct TcpServer {
    pub config: TcpConfig,
    // 私有字段...
}
```

**方法：**

- `TcpServer::new(config: TcpConfig) -> (Self, TcpHandle)` - 创建 TCP 服务器和句柄
- `run(&mut self) -> TcpResult<()>` - 运行 TCP 服务器主循环

#### TcpHandle 结构体

用于与 TCP 服务器通信的句柄。

**方法：**

- `start(&self) -> TcpResult<()>` - 启动 TCP 服务器
- `stop(&self) -> TcpResult<()>` - 停止 TCP 服务器
- `send_to_client(&self, client_id: ClientId, data: Vec<u8>) -> TcpResult<()>` - 发送数据到指定客户端
- `send_to_all(&self, data: Vec<u8>) -> TcpResult<()>` - 发送数据到所有客户端
- `disconnect_client(&self, client_id: ClientId) -> TcpResult<()>` - 断开指定客户端
- `recv_event(&mut self) -> Option<TcpEvent>` - 接收 TCP 事件
- `get_status(&self) -> TcpResult<()>` - 请求状态更新
- `shutdown(&self) -> TcpResult<()>` - 关闭 TCP 服务器

#### ClientId 结构体

唯一标识 TCP 客户端。

```rust
pub struct ClientId(u64);
```

**方法：**

- `ClientId::new() -> Self` - 生成新的客户端 ID
- `as_u64(&self) -> u64` - 获取 ID 的数值

#### TcpEvent 枚举

TCP 服务器事件类型。

```rust
pub enum TcpEvent {
    ServerStarted { bind_addr: SocketAddr },
    ServerStopped,
    ClientConnected { client_id: ClientId, addr: SocketAddr },
    ClientDisconnected { client_id: ClientId, addr: SocketAddr, reason: String },
    DataReceived { client_id: ClientId, data: Vec<u8>, timestamp: Instant },
    Error { error: TcpError, timestamp: Instant },
    StatusUpdate { client_count: usize, total_bytes_sent: u64, total_bytes_received: u64 },
}
```

#### TcpCommand 枚举

可发送给 TCP 服务器的命令。

```rust
pub enum TcpCommand {
    Start,
    Stop,
    SendToClient { client_id: ClientId, data: Vec<u8> },
    SendToAll { data: Vec<u8> },
    DisconnectClient { client_id: ClientId },
    GetStatus,
    Shutdown,
}
```

### 4. DataForwarder 模块

#### DataForwarder 结构体

核心数据转发器，协调串口和 TCP 之间的数据流。

```rust
pub struct DataForwarder {
    // 私有字段...
}
```

**方法：**

- `DataForwarder::new(serial_handle: SerialHandle, tcp_handle: TcpHandle) -> Self` - 创建数据转发器
- `run(&mut self) -> ForwarderResult<()>` - 运行数据转发器主循环
- `get_statistics(&self) -> ForwarderStatistics` - 获取统计信息
- `register_event_handler(&self, event_type: String, handler: Box<dyn EventHandler + Send + Sync>)` - 注册事件处理器
- `shutdown(&mut self) -> ForwarderResult<()>` - 关闭数据转发器

#### ForwarderStatistics 结构体

数据转发统计信息。

```rust
pub struct ForwarderStatistics {
    pub bytes_serial_to_tcp: u64,
    pub bytes_tcp_to_serial: u64,
    pub messages_serial_to_tcp: u64,
    pub messages_tcp_to_serial: u64,
    pub active_clients: usize,
    pub errors: u64,
    pub start_time: Instant,
    pub last_activity: Instant,
}
```

#### MessageQueue 结构体

消息队列，用于缓冲数据。

```rust
pub struct MessageQueue {
    // 私有字段...
}
```

**方法：**

- `MessageQueue::new(max_size: usize) -> Self` - 创建消息队列
- `push_serial_to_tcp(&mut self, data: Vec<u8>) -> ForwarderResult<()>` - 添加串口到 TCP 的消息
- `push_tcp_to_serial(&mut self, client_id: ClientId, data: Vec<u8>) -> ForwarderResult<()>` - 添加 TCP 到串口的消息
- `pop_serial_to_tcp(&mut self) -> Option<Vec<u8>>` - 取出串口到 TCP 的消息
- `pop_tcp_to_serial(&mut self) -> Option<(ClientId, Vec<u8>)>` - 取出 TCP 到串口的消息
- `is_empty(&self) -> bool` - 检查队列是否为空
- `len(&self) -> (usize, usize)` - 获取队列长度

#### EventHandler Trait

事件处理器接口。

```rust
pub trait EventHandler {
    async fn handle(&self, data: &[u8]) -> ForwarderResult<()>;
}
```

### 5. Error 模块

#### Com2TcpError 枚举

主要错误类型。

```rust
pub enum Com2TcpError {
    Serial(SerialError),
    Tcp(TcpError),
    Config(String),
    Io(std::io::Error),
    Forwarder(ForwarderError),
    Application(String),
}
```

#### SerialError 枚举

串口相关错误。

```rust
pub enum SerialError {
    OpenFailed { port: String, source: serialport::Error },
    Disconnected { port: String },
    ReadTimeout { port: String },
    WriteFailed { port: String, source: serialport::Error },
    InvalidConfig { message: String },
    PortNotAvailable { port: String },
    OperationFailed(serialport::Error),
}
```

#### TcpError 枚举

TCP 相关错误。

```rust
pub enum TcpError {
    BindFailed { host: String, port: u16, source: std::io::Error },
    ConnectionFailed { source: std::io::Error },
    ClientDisconnected { client_id: u64 },
    SendFailed { client_id: u64, source: std::io::Error },
    ReceiveFailed { client_id: u64, source: std::io::Error },
    MaxClientsExceeded { max: usize },
    ClientTimeout { client_id: u64 },
    InvalidConfig { message: String },
}
```

#### ForwarderError 枚举

数据转发相关错误。

```rust
pub enum ForwarderError {
    QueueFull { size: usize },
    SerialToTcpFailed { source: Box<dyn std::error::Error + Send + Sync> },
    TcpToSerialFailed { source: Box<dyn std::error::Error + Send + Sync> },
    DispatcherError { message: String },
    InvalidMessage { message: String },
    BufferOverflow { attempted: usize, capacity: usize },
    ChannelError { message: String },
}
```

## 使用示例

### 基本使用

```rust
use com2tcp::{Config, DataForwarder, SerialManager, TcpServer};
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建配置
    let config = Config::default();
    
    // 创建组件
    let (serial_manager, serial_handle) = SerialManager::new(config.serial);
    let (tcp_server, tcp_handle) = TcpServer::new(config.tcp);
    let mut data_forwarder = DataForwarder::new(serial_handle, tcp_handle);
    
    // 启动组件
    tokio::spawn(async move { serial_manager.run().await });
    tokio::spawn(async move { tcp_server.run().await });
    
    // 运行数据转发器
    data_forwarder.run().await?;
    
    Ok(())
}
```

### 事件处理

```rust
use com2tcp::{SerialEvent, TcpEvent};

// 处理串口事件
async fn handle_serial_events(mut serial_handle: SerialHandle) {
    while let Some(event) = serial_handle.recv_event().await {
        match event {
            SerialEvent::Connected { port_name, .. } => {
                println!("串口已连接: {}", port_name);
            }
            SerialEvent::DataReceived { data, .. } => {
                println!("接收到数据: {} 字节", data.len());
            }
            SerialEvent::Error { error, .. } => {
                eprintln!("串口错误: {}", error);
            }
            _ => {}
        }
    }
}

// 处理 TCP 事件
async fn handle_tcp_events(mut tcp_handle: TcpHandle) {
    while let Some(event) = tcp_handle.recv_event().await {
        match event {
            TcpEvent::ClientConnected { client_id, addr } => {
                println!("客户端连接: {} from {}", client_id, addr);
            }
            TcpEvent::DataReceived { client_id, data, .. } => {
                println!("从客户端 {} 接收到 {} 字节", client_id, data.len());
            }
            TcpEvent::ClientDisconnected { client_id, reason, .. } => {
                println!("客户端 {} 断开连接: {}", client_id, reason);
            }
            _ => {}
        }
    }
}
```

### 自定义事件处理器

```rust
use com2tcp::data_forwarder::{EventHandler, ForwarderResult};

struct CustomEventHandler;

impl EventHandler for CustomEventHandler {
    async fn handle(&self, data: &[u8]) -> ForwarderResult<()> {
        // 自定义数据处理逻辑
        println!("处理数据: {:?}", data);
        Ok(())
    }
}

// 注册事件处理器
let handler = Box::new(CustomEventHandler);
data_forwarder.register_event_handler("custom_event".to_string(), handler).await;
```

## 类型别名

```rust
pub type Result<T> = std::result::Result<T, Com2TcpError>;
pub type SerialResult<T> = std::result::Result<T, SerialError>;
pub type TcpResult<T> = std::result::Result<T, TcpError>;
pub type ForwarderResult<T> = std::result::Result<T, ForwarderError>;
```

## 常量

```rust
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const APP_NAME: &str = env!("CARGO_PKG_NAME");
pub const APP_DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");
```
