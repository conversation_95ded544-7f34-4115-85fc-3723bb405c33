use clap::{Parse<PERSON>, ValueEnum};
use crate::error::{Com2TcpError, Result};

/// COM2TCP - Serial port to TCP forwarder
#[derive(Parser)]
#[command(
    name = "com2tcp",
    version = env!("CARGO_PKG_VERSION"),
    about = "A bidirectional serial port to TCP forwarder supporting multiple concurrent clients",
    long_about = "COM2TCP creates a TCP server that forwards data bidirectionally between \
                  a serial port and multiple TCP clients. All clients receive data from the \
                  serial port, and data from any client is forwarded to the serial port."
)]
pub struct Args {
    /// Serial port device path (e.g., COM1, /dev/ttyUSB0)
    #[arg(short, long, default_value = "COM1")]
    pub port: String,

    /// Baud rate for serial communication
    #[arg(short, long, default_value = "9600")]
    pub baud: u32,

    /// Number of data bits (5, 6, 7, 8)
    #[arg(short = 'd', long, default_value = "8")]
    pub data_bits: u8,

    /// Number of stop bits (1, 2)
    #[arg(short = 's', long, default_value = "1")]
    pub stop_bits: u8,

    /// Parity setting (none, odd, even)
    #[arg(long, default_value = "none")]
    pub parity: String,

    /// Serial port timeout in milliseconds
    #[arg(long, default_value = "1000")]
    pub timeout: u64,

    /// TCP server listening port
    #[arg(short = 't', long, default_value = "8080")]
    pub tcp_port: u16,

    /// TCP server bind address
    #[arg(long, default_value = "0.0.0.0")]
    pub tcp_host: String,

    /// Maximum number of concurrent TCP clients
    #[arg(long, default_value = "10")]
    pub max_clients: usize,

    /// Client connection timeout in seconds
    #[arg(long, default_value = "300")]
    pub client_timeout: u64,

    /// Buffer size for data transfer
    #[arg(long, default_value = "1024")]
    pub buffer_size: usize,

    /// Maximum message size in bytes
    #[arg(long, default_value = "1048576")] // 1MB
    pub max_message_size: usize,

    /// Maximum queue size for message buffering
    #[arg(long, default_value = "1000")]
    pub max_queue_size: usize,

    /// Log level (off, error, warn, info, debug, trace)
    #[arg(long, default_value = "info")]
    pub log_level: String,

    /// Log file path (if not specified, logs to stdout)
    #[arg(long)]
    pub log_file: Option<String>,

    /// Enable verbose output
    #[arg(short, long)]
    pub verbose: bool,

    /// Configuration file path
    #[arg(short, long)]
    pub config: Option<String>,

    /// Generate default configuration file
    #[arg(long)]
    pub generate_config: Option<String>,

    /// List available serial ports
    #[arg(long)]
    pub list_ports: bool,

    /// Dry run mode - validate configuration without starting
    #[arg(long)]
    pub dry_run: bool,
}

/// Parse command line arguments
pub fn parse_args() -> Args {
    Args::parse()
}

/// Validate command line arguments
pub fn validate_args(args: &Args) -> Result<()> {
    // Validate serial port settings
    if args.port.is_empty() {
        return Err(Com2TcpError::config_error("Serial port cannot be empty"));
    }

    if args.baud == 0 {
        return Err(Com2TcpError::config_error("Baud rate must be greater than 0"));
    }

    if ![5, 6, 7, 8].contains(&args.data_bits) {
        return Err(Com2TcpError::config_error("Data bits must be 5, 6, 7, or 8"));
    }

    if ![1, 2].contains(&args.stop_bits) {
        return Err(Com2TcpError::config_error("Stop bits must be 1 or 2"));
    }

    let valid_parity = ["none", "n", "odd", "o", "even", "e"];
    if !valid_parity.contains(&args.parity.to_lowercase().as_str()) {
        return Err(Com2TcpError::config_error("Parity must be 'none', 'odd', or 'even'"));
    }

    // Validate TCP settings
    if args.tcp_port == 0 {
        return Err(Com2TcpError::config_error("TCP port must be greater than 0"));
    }

    if args.tcp_host.is_empty() {
        return Err(Com2TcpError::config_error("TCP host cannot be empty"));
    }

    if args.max_clients == 0 {
        return Err(Com2TcpError::config_error("Max clients must be greater than 0"));
    }

    if args.max_clients > 1000 {
        return Err(Com2TcpError::config_error("Max clients cannot exceed 1000"));
    }

    // Validate buffer and message sizes
    if args.buffer_size == 0 {
        return Err(Com2TcpError::config_error("Buffer size must be greater than 0"));
    }

    if args.buffer_size > 1024 * 1024 * 10 { // 10MB
        return Err(Com2TcpError::config_error("Buffer size cannot exceed 10MB"));
    }

    if args.max_message_size == 0 {
        return Err(Com2TcpError::config_error("Max message size must be greater than 0"));
    }

    if args.max_message_size > 1024 * 1024 * 100 { // 100MB
        return Err(Com2TcpError::config_error("Max message size cannot exceed 100MB"));
    }

    if args.max_queue_size == 0 {
        return Err(Com2TcpError::config_error("Max queue size must be greater than 0"));
    }

    // Validate log level
    let valid_log_levels = ["off", "error", "warn", "info", "debug", "trace"];
    if !valid_log_levels.contains(&args.log_level.to_lowercase().as_str()) {
        return Err(Com2TcpError::config_error("Invalid log level"));
    }

    Ok(())
}

/// List available serial ports
pub fn list_serial_ports() -> Result<()> {
    println!("Available serial ports:");
    
    match serialport::available_ports() {
        Ok(ports) => {
            if ports.is_empty() {
                println!("  No serial ports found");
            } else {
                for port in ports {
                    println!("  {} - {}", port.port_name, port.port_type.description());
                }
            }
        }
        Err(e) => {
            return Err(Com2TcpError::config_error(&format!("Failed to list serial ports: {}", e)));
        }
    }
    
    Ok(())
}

/// Generate default configuration file
pub fn generate_default_config(path: &str) -> Result<()> {
    use crate::config::Config;
    
    let config = Config::default();
    config.save_to_file(path)?;
    
    println!("Default configuration saved to: {}", path);
    Ok(())
}

/// Handle special command line operations that don't require full application startup
pub fn handle_special_operations(args: &Args) -> Result<bool> {
    // List serial ports
    if args.list_ports {
        list_serial_ports()?;
        return Ok(true);
    }
    
    // Generate default configuration
    if let Some(config_path) = &args.generate_config {
        generate_default_config(config_path)?;
        return Ok(true);
    }
    
    Ok(false)
}

/// Print application banner
pub fn print_banner() {
    println!("COM2TCP v{}", env!("CARGO_PKG_VERSION"));
    println!("Serial port to TCP forwarder");
    println!("Press Ctrl+C to stop");
    println!();
}

/// Print configuration summary
pub fn print_config_summary(config: &crate::config::Config) {
    println!("Configuration:");
    println!("  Serial Port: {} @ {} baud", config.serial.port, config.serial.baud_rate);
    println!("  Data: {} bits, Stop: {:?}, Parity: {:?}", 
             match config.serial.data_bits {
                 serialport::DataBits::Five => 5,
                 serialport::DataBits::Six => 6,
                 serialport::DataBits::Seven => 7,
                 serialport::DataBits::Eight => 8,
             },
             config.serial.stop_bits,
             config.serial.parity);
    println!("  TCP Server: {}:{}", config.tcp.host, config.tcp.port);
    println!("  Max Clients: {}", config.tcp.max_clients);
    println!("  Buffer Size: {} bytes", config.tcp.buffer_size);
    println!();
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_validate_args_valid() {
        let args = Args {
            port: "COM1".to_string(),
            baud: 9600,
            data_bits: 8,
            stop_bits: 1,
            parity: "none".to_string(),
            timeout: 1000,
            tcp_port: 8080,
            tcp_host: "127.0.0.1".to_string(),
            max_clients: 10,
            client_timeout: 300,
            buffer_size: 1024,
            max_message_size: 1048576,
            max_queue_size: 1000,
            log_level: "info".to_string(),
            log_file: None,
            verbose: false,
            config: None,
            generate_config: None,
            list_ports: false,
            dry_run: false,
        };
        
        assert!(validate_args(&args).is_ok());
    }
    
    #[test]
    fn test_validate_args_invalid_port() {
        let mut args = Args {
            port: String::new(),
            baud: 9600,
            data_bits: 8,
            stop_bits: 1,
            parity: "none".to_string(),
            timeout: 1000,
            tcp_port: 8080,
            tcp_host: "127.0.0.1".to_string(),
            max_clients: 10,
            client_timeout: 300,
            buffer_size: 1024,
            max_message_size: 1048576,
            max_queue_size: 1000,
            log_level: "info".to_string(),
            log_file: None,
            verbose: false,
            config: None,
            generate_config: None,
            list_ports: false,
            dry_run: false,
        };
        
        assert!(validate_args(&args).is_err());
    }
    
    #[test]
    fn test_validate_args_invalid_data_bits() {
        let args = Args {
            port: "COM1".to_string(),
            baud: 9600,
            data_bits: 9, // Invalid
            stop_bits: 1,
            parity: "none".to_string(),
            timeout: 1000,
            tcp_port: 8080,
            tcp_host: "127.0.0.1".to_string(),
            max_clients: 10,
            client_timeout: 300,
            buffer_size: 1024,
            max_message_size: 1048576,
            max_queue_size: 1000,
            log_level: "info".to_string(),
            log_file: None,
            verbose: false,
            config: None,
            generate_config: None,
            list_ports: false,
            dry_run: false,
        };
        
        assert!(validate_args(&args).is_err());
    }
}
