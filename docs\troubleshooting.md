# COM2TCP 故障排除指南

## 常见问题和解决方案

### 1. 串口相关问题

#### 问题：无法打开串口

**错误信息：**
```
Error: Serial port error: Failed to open port 'COM1': Access denied
```

**可能原因：**
- 串口被其他程序占用
- 权限不足
- 串口不存在
- 驱动程序问题

**解决方案：**

1. **检查串口是否存在：**
   ```bash
   com2tcp --list-ports
   ```

2. **Windows 系统：**
   - 打开设备管理器，检查串口状态
   - 确保没有其他程序使用该串口
   - 以管理员身份运行程序
   - 检查串口驱动是否正确安装

3. **Linux 系统：**
   ```bash
   # 检查串口设备
   ls -l /dev/tty*
   
   # 检查权限
   sudo chmod 666 /dev/ttyUSB0
   
   # 将用户添加到 dialout 组
   sudo usermod -a -G dialout $USER
   
   # 检查是否被其他进程占用
   sudo lsof /dev/ttyUSB0
   ```

4. **macOS 系统：**
   ```bash
   # 检查串口设备
   ls -l /dev/cu.*
   
   # 检查权限
   sudo chmod 666 /dev/cu.usbserial-*
   ```

#### 问题：串口连接不稳定

**症状：**
- 频繁断开重连
- 数据丢失
- 读取超时

**解决方案：**

1. **调整超时设置：**
   ```bash
   com2tcp --port COM1 --timeout 5000  # 增加到5秒
   ```

2. **检查硬件连接：**
   - 确保串口线缆连接牢固
   - 检查串口线缆质量
   - 尝试更换 USB 端口

3. **调整串口参数：**
   ```bash
   com2tcp --port COM1 --baud 9600 --data-bits 8 --stop-bits 1 --parity none
   ```

4. **启用详细日志：**
   ```bash
   com2tcp --port COM1 --verbose --log-level debug
   ```

#### 问题：数据传输错误

**症状：**
- 接收到乱码
- 数据不完整
- 校验错误

**解决方案：**

1. **确认串口参数：**
   - 波特率必须与设备匹配
   - 数据位、停止位、校验位设置正确

2. **检查数据流控制：**
   - 某些设备需要硬件流控制
   - 检查 RTS/CTS 或 DTR/DSR 设置

3. **调整缓冲区大小：**
   ```bash
   com2tcp --port COM1 --buffer-size 4096
   ```

### 2. TCP 网络相关问题

#### 问题：TCP 端口绑定失败

**错误信息：**
```
Error: TCP server error: Failed to bind to 0.0.0.0:8080: Address already in use
```

**解决方案：**

1. **检查端口占用：**
   ```bash
   # Windows
   netstat -an | findstr :8080
   
   # Linux/macOS
   netstat -tulpn | grep :8080
   lsof -i :8080
   ```

2. **使用其他端口：**
   ```bash
   com2tcp --port COM1 --tcp-port 8081
   ```

3. **终止占用端口的进程：**
   ```bash
   # Windows
   taskkill /PID <进程ID> /F
   
   # Linux/macOS
   kill -9 <进程ID>
   ```

4. **等待端口释放：**
   - 有时需要等待几分钟让系统释放端口
   - 重启网络服务或系统

#### 问题：客户端无法连接

**症状：**
- 连接超时
- 连接被拒绝
- 无法建立连接

**解决方案：**

1. **检查防火墙设置：**
   ```bash
   # Windows
   # 在 Windows 防火墙中添加端口例外
   
   # Linux (iptables)
   sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
   
   # Linux (ufw)
   sudo ufw allow 8080
   ```

2. **检查绑定地址：**
   ```bash
   # 绑定到所有接口
   com2tcp --port COM1 --tcp-host 0.0.0.0 --tcp-port 8080
   
   # 只绑定到本地
   com2tcp --port COM1 --tcp-host 127.0.0.1 --tcp-port 8080
   ```

3. **测试网络连接：**
   ```bash
   # 测试本地连接
   telnet localhost 8080
   
   # 测试远程连接
   telnet <服务器IP> 8080
   ```

#### 问题：客户端连接频繁断开

**症状：**
- 客户端超时断开
- 连接不稳定
- 数据传输中断

**解决方案：**

1. **调整客户端超时：**
   ```bash
   com2tcp --port COM1 --client-timeout 600  # 10分钟
   ```

2. **限制客户端数量：**
   ```bash
   com2tcp --port COM1 --max-clients 5
   ```

3. **检查网络质量：**
   - 使用 ping 测试网络延迟
   - 检查网络带宽
   - 避免网络拥塞时段

### 3. 性能相关问题

#### 问题：数据传输延迟高

**症状：**
- 数据传输慢
- 响应时间长
- 实时性差

**解决方案：**

1. **优化缓冲区设置：**
   ```bash
   com2tcp --port COM1 --buffer-size 8192 --max-queue-size 2000
   ```

2. **提高串口波特率：**
   ```bash
   com2tcp --port COM1 --baud 115200
   ```

3. **减少日志输出：**
   ```bash
   com2tcp --port COM1 --log-level error
   ```

4. **限制客户端数量：**
   ```bash
   com2tcp --port COM1 --max-clients 3
   ```

#### 问题：内存使用过高

**症状：**
- 程序内存占用持续增长
- 系统响应变慢
- 可能出现内存不足

**解决方案：**

1. **限制队列大小：**
   ```bash
   com2tcp --port COM1 --max-queue-size 500
   ```

2. **限制消息大小：**
   ```bash
   com2tcp --port COM1 --max-message-size 65536  # 64KB
   ```

3. **定期重启程序：**
   - 设置定时任务重启程序
   - 监控内存使用情况

### 4. 配置相关问题

#### 问题：配置文件格式错误

**错误信息：**
```
Error: Configuration error: Failed to parse config file 'config.toml': invalid TOML
```

**解决方案：**

1. **生成默认配置：**
   ```bash
   com2tcp --generate-config config.toml
   ```

2. **验证 TOML 格式：**
   - 使用在线 TOML 验证器
   - 检查语法错误（引号、括号等）

3. **检查配置项：**
   ```toml
   [serial]
   port = "COM1"
   baud_rate = 9600
   
   [tcp]
   host = "0.0.0.0"
   port = 8080
   ```

#### 问题：参数验证失败

**错误信息：**
```
Error: Configuration error: Invalid data bits: 9
```

**解决方案：**

1. **检查参数范围：**
   - 数据位：5, 6, 7, 8
   - 停止位：1, 2
   - 校验位：none, odd, even

2. **使用有效参数：**
   ```bash
   com2tcp --port COM1 --data-bits 8 --stop-bits 1 --parity none
   ```

### 5. 调试技巧

#### 启用详细日志

```bash
com2tcp --port COM1 --verbose --log-level debug --log-file debug.log
```

#### 使用干运行模式

```bash
com2tcp --port COM1 --dry-run
```

#### 监控系统资源

```bash
# Windows
tasklist | findstr com2tcp

# Linux
ps aux | grep com2tcp
top -p $(pgrep com2tcp)

# 监控网络连接
netstat -an | grep 8080
```

#### 网络调试工具

```bash
# 使用 telnet 测试连接
telnet localhost 8080

# 使用 netcat 测试
nc localhost 8080

# 使用 tcpdump 抓包
sudo tcpdump -i lo port 8080
```

### 6. 常见错误代码

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| E001 | 串口打开失败 | 检查串口是否存在和权限 |
| E002 | TCP 端口绑定失败 | 更换端口或检查防火墙 |
| E003 | 配置验证失败 | 检查配置参数有效性 |
| E004 | 内存不足 | 减少缓冲区大小或重启程序 |
| E005 | 网络连接超时 | 检查网络连接和防火墙 |

### 7. 获取帮助

如果以上解决方案都无法解决问题，请：

1. **收集诊断信息：**
   ```bash
   com2tcp --port COM1 --verbose --log-level debug --log-file diagnostic.log
   ```

2. **记录错误详情：**
   - 完整的错误信息
   - 使用的命令行参数
   - 操作系统版本
   - 硬件信息

3. **提供复现步骤：**
   - 详细的操作步骤
   - 预期结果和实际结果
   - 环境配置信息

4. **联系支持：**
   - 提交 GitHub Issue
   - 发送邮件到支持邮箱
   - 查看在线文档和 FAQ
