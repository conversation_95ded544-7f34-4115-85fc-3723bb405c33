use crate::error::{ForwarderError, ForwarderResult};
use crate::serial_manager::{SerialEvent, SerialHandle};
use crate::tcp_server::{ClientId, TcpEvent, TcpHandle};
use log::{debug, error, info, warn};
use std::collections::{HashMap, VecDeque};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::{mpsc, Mutex};
use tokio::time::{interval, Duration};

/// Statistics for data forwarding operations
#[derive(Debug, Clone)]
pub struct ForwarderStatistics {
    pub bytes_serial_to_tcp: u64,
    pub bytes_tcp_to_serial: u64,
    pub messages_serial_to_tcp: u64,
    pub messages_tcp_to_serial: u64,
    pub active_clients: usize,
    pub errors: u64,
    pub start_time: Instant,
    pub last_activity: Instant,
}

impl Default for ForwarderStatistics {
    fn default() -> Self {
        let now = Instant::now();
        Self {
            bytes_serial_to_tcp: 0,
            bytes_tcp_to_serial: 0,
            messages_serial_to_tcp: 0,
            messages_tcp_to_serial: 0,
            active_clients: 0,
            errors: 0,
            start_time: now,
            last_activity: now,
        }
    }
}

/// Message queue for buffering data between serial and TCP
#[derive(Debug)]
pub struct MessageQueue {
    serial_to_tcp: VecDeque<Vec<u8>>,
    tcp_to_serial: VecDeque<(ClientId, Vec<u8>)>,
    max_size: usize,
}

impl MessageQueue {
    pub fn new(max_size: usize) -> Self {
        Self {
            serial_to_tcp: VecDeque::new(),
            tcp_to_serial: VecDeque::new(),
            max_size,
        }
    }
    
    pub fn push_serial_to_tcp(&mut self, data: Vec<u8>) -> ForwarderResult<()> {
        if self.serial_to_tcp.len() >= self.max_size {
            return Err(ForwarderError::queue_full(self.serial_to_tcp.len()));
        }
        self.serial_to_tcp.push_back(data);
        Ok(())
    }
    
    pub fn push_tcp_to_serial(&mut self, client_id: ClientId, data: Vec<u8>) -> ForwarderResult<()> {
        if self.tcp_to_serial.len() >= self.max_size {
            return Err(ForwarderError::queue_full(self.tcp_to_serial.len()));
        }
        self.tcp_to_serial.push_back((client_id, data));
        Ok(())
    }
    
    pub fn pop_serial_to_tcp(&mut self) -> Option<Vec<u8>> {
        self.serial_to_tcp.pop_front()
    }
    
    pub fn pop_tcp_to_serial(&mut self) -> Option<(ClientId, Vec<u8>)> {
        self.tcp_to_serial.pop_front()
    }
    
    pub fn is_empty(&self) -> bool {
        self.serial_to_tcp.is_empty() && self.tcp_to_serial.is_empty()
    }
    
    pub fn len(&self) -> (usize, usize) {
        (self.serial_to_tcp.len(), self.tcp_to_serial.len())
    }
}

/// Event dispatcher for handling different types of events
pub struct EventDispatcher {
    handlers: HashMap<String, Box<dyn EventHandler + Send + Sync>>,
}

impl EventDispatcher {
    pub fn new() -> Self {
        Self {
            handlers: HashMap::new(),
        }
    }
    
    pub fn register_handler(&mut self, event_type: String, handler: Box<dyn EventHandler + Send + Sync>) {
        self.handlers.insert(event_type, handler);
    }
    
    pub async fn dispatch(&self, event_type: &str, data: &[u8]) -> ForwarderResult<()> {
        if let Some(handler) = self.handlers.get(event_type) {
            handler.handle(data).await
        } else {
            debug!("No handler registered for event type: {}", event_type);
            Ok(())
        }
    }
}

/// Trait for event handlers
pub trait EventHandler {
    async fn handle(&self, data: &[u8]) -> ForwarderResult<()>;
}

/// Core data forwarder that coordinates between serial and TCP
pub struct DataForwarder {
    serial_handle: SerialHandle,
    tcp_handle: TcpHandle,
    message_queue: Arc<Mutex<MessageQueue>>,
    event_dispatcher: Arc<Mutex<EventDispatcher>>,
    statistics: Arc<Mutex<ForwarderStatistics>>,
    connected_clients: Arc<Mutex<HashMap<ClientId, Instant>>>,
    is_running: bool,
}

impl DataForwarder {
    /// Create a new data forwarder
    pub fn new(serial_handle: SerialHandle, tcp_handle: TcpHandle) -> Self {
        Self {
            serial_handle,
            tcp_handle,
            message_queue: Arc::new(Mutex::new(MessageQueue::new(1000))),
            event_dispatcher: Arc::new(Mutex::new(EventDispatcher::new())),
            statistics: Arc::new(Mutex::new(ForwarderStatistics::default())),
            connected_clients: Arc::new(Mutex::new(HashMap::new())),
            is_running: false,
        }
    }
    
    /// Run the data forwarder main loop
    pub async fn run(&mut self) -> ForwarderResult<()> {
        info!("Starting data forwarder");
        self.is_running = true;
        
        // Start the TCP server
        if let Err(e) = self.tcp_handle.start().await {
            error!("Failed to start TCP server: {}", e);
            return Err(ForwarderError::TcpToSerialFailed {
                source: Box::new(e),
            });
        }
        
        // Connect to serial port
        if let Err(e) = self.serial_handle.connect().await {
            warn!("Failed to connect to serial port: {}", e);
        }
        
        let mut stats_interval = interval(Duration::from_secs(60));
        let mut queue_process_interval = interval(Duration::from_millis(10));
        
        while self.is_running {
            tokio::select! {
                // Handle serial events
                event = self.serial_handle.recv_event() => {
                    if let Some(event) = event {
                        if let Err(e) = self.handle_serial_event(event).await {
                            error!("Error handling serial event: {}", e);
                            self.increment_error_count().await;
                        }
                    }
                }
                
                // Handle TCP events
                event = self.tcp_handle.recv_event() => {
                    if let Some(event) = event {
                        if let Err(e) = self.handle_tcp_event(event).await {
                            error!("Error handling TCP event: {}", e);
                            self.increment_error_count().await;
                        }
                    }
                }
                
                // Process message queue
                _ = queue_process_interval.tick() => {
                    if let Err(e) = self.process_message_queue().await {
                        error!("Error processing message queue: {}", e);
                        self.increment_error_count().await;
                    }
                }
                
                // Print statistics periodically
                _ = stats_interval.tick() => {
                    self.print_statistics().await;
                }
            }
        }
        
        info!("Data forwarder stopped");
        Ok(())
    }
    
    /// Handle serial port events
    async fn handle_serial_event(&mut self, event: SerialEvent) -> ForwarderResult<()> {
        match event {
            SerialEvent::Connected { port_name, .. } => {
                info!("Serial port connected: {}", port_name);
            }
            
            SerialEvent::Disconnected { port_name, reason } => {
                warn!("Serial port disconnected: {} ({})", port_name, reason);
                // Try to reconnect after a delay
                tokio::spawn(async move {
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    // TODO: Implement reconnection logic
                });
            }
            
            SerialEvent::DataReceived { data, .. } => {
                debug!("Received {} bytes from serial port", data.len());
                self.forward_serial_to_tcp(data).await?;
            }
            
            SerialEvent::Error { error, .. } => {
                error!("Serial port error: {}", error);
                self.increment_error_count().await;
            }
            
            SerialEvent::StatusUpdate { .. } => {
                // Handle status updates if needed
            }
        }
        
        Ok(())
    }
    
    /// Handle TCP server events
    async fn handle_tcp_event(&mut self, event: TcpEvent) -> ForwarderResult<()> {
        match event {
            TcpEvent::ServerStarted { bind_addr } => {
                info!("TCP server started on {}", bind_addr);
            }
            
            TcpEvent::ServerStopped => {
                info!("TCP server stopped");
            }
            
            TcpEvent::ClientConnected { client_id, addr } => {
                info!("Client {} connected from {}", client_id, addr);
                let mut clients = self.connected_clients.lock().await;
                clients.insert(client_id, Instant::now());
                
                // Update statistics
                let mut stats = self.statistics.lock().await;
                stats.active_clients = clients.len();
            }
            
            TcpEvent::ClientDisconnected { client_id, addr, reason } => {
                info!("Client {} disconnected from {} ({})", client_id, addr, reason);
                let mut clients = self.connected_clients.lock().await;
                clients.remove(&client_id);
                
                // Update statistics
                let mut stats = self.statistics.lock().await;
                stats.active_clients = clients.len();
            }
            
            TcpEvent::DataReceived { client_id, data, .. } => {
                debug!("Received {} bytes from client {}", data.len(), client_id);
                self.forward_tcp_to_serial(client_id, data).await?;
            }
            
            TcpEvent::Error { error, .. } => {
                error!("TCP server error: {}", error);
                self.increment_error_count().await;
            }
            
            TcpEvent::StatusUpdate { .. } => {
                // Handle status updates if needed
            }
        }
        
        Ok(())
    }

    /// Forward data from serial port to TCP clients
    async fn forward_serial_to_tcp(&mut self, data: Vec<u8>) -> ForwarderResult<()> {
        // Add to message queue
        {
            let mut queue = self.message_queue.lock().await;
            queue.push_serial_to_tcp(data.clone())?;
        }

        // Update statistics
        {
            let mut stats = self.statistics.lock().await;
            stats.bytes_serial_to_tcp += data.len() as u64;
            stats.messages_serial_to_tcp += 1;
            stats.last_activity = Instant::now();
        }

        // Dispatch event
        {
            let dispatcher = self.event_dispatcher.lock().await;
            dispatcher.dispatch("serial_to_tcp", &data).await?;
        }

        Ok(())
    }

    /// Forward data from TCP client to serial port
    async fn forward_tcp_to_serial(&mut self, client_id: ClientId, data: Vec<u8>) -> ForwarderResult<()> {
        // Add to message queue
        {
            let mut queue = self.message_queue.lock().await;
            queue.push_tcp_to_serial(client_id, data.clone())?;
        }

        // Update statistics
        {
            let mut stats = self.statistics.lock().await;
            stats.bytes_tcp_to_serial += data.len() as u64;
            stats.messages_tcp_to_serial += 1;
            stats.last_activity = Instant::now();
        }

        // Dispatch event
        {
            let dispatcher = self.event_dispatcher.lock().await;
            dispatcher.dispatch("tcp_to_serial", &data).await?;
        }

        Ok(())
    }

    /// Process the message queue
    async fn process_message_queue(&mut self) -> ForwarderResult<()> {
        let mut queue = self.message_queue.lock().await;

        // Process serial to TCP messages
        while let Some(data) = queue.pop_serial_to_tcp() {
            if let Err(e) = self.tcp_handle.send_to_all(data).await {
                error!("Failed to send data to TCP clients: {}", e);
                return Err(ForwarderError::SerialToTcpFailed {
                    source: Box::new(e),
                });
            }
        }

        // Process TCP to serial messages
        while let Some((client_id, data)) = queue.pop_tcp_to_serial() {
            if let Err(e) = self.serial_handle.send_data(data).await {
                error!("Failed to send data to serial port from client {}: {}", client_id, e);
                return Err(ForwarderError::TcpToSerialFailed {
                    source: Box::new(e),
                });
            }
        }

        Ok(())
    }

    /// Increment error count in statistics
    async fn increment_error_count(&self) {
        let mut stats = self.statistics.lock().await;
        stats.errors += 1;
    }

    /// Print current statistics
    async fn print_statistics(&self) {
        let stats = self.statistics.lock().await;
        let uptime = stats.start_time.elapsed();

        info!("Forwarder Statistics:");
        info!("  Uptime: {:?}", uptime);
        info!("  Active clients: {}", stats.active_clients);
        info!("  Serial -> TCP: {} bytes ({} messages)",
              stats.bytes_serial_to_tcp, stats.messages_serial_to_tcp);
        info!("  TCP -> Serial: {} bytes ({} messages)",
              stats.bytes_tcp_to_serial, stats.messages_tcp_to_serial);
        info!("  Errors: {}", stats.errors);
        info!("  Last activity: {:?} ago", stats.last_activity.elapsed());

        // Print queue status
        let queue = self.message_queue.lock().await;
        let (serial_queue_len, tcp_queue_len) = queue.len();
        if serial_queue_len > 0 || tcp_queue_len > 0 {
            info!("  Queue: {} serial->tcp, {} tcp->serial", serial_queue_len, tcp_queue_len);
        }
    }

    /// Get current statistics
    pub async fn get_statistics(&self) -> ForwarderStatistics {
        let stats = self.statistics.lock().await;
        stats.clone()
    }

    /// Register an event handler
    pub async fn register_event_handler(&self, event_type: String, handler: Box<dyn EventHandler + Send + Sync>) {
        let mut dispatcher = self.event_dispatcher.lock().await;
        dispatcher.register_handler(event_type, handler);
    }

    /// Shutdown the data forwarder
    pub async fn shutdown(&mut self) -> ForwarderResult<()> {
        info!("Shutting down data forwarder");
        self.is_running = false;

        // Shutdown TCP server
        if let Err(e) = self.tcp_handle.shutdown().await {
            error!("Error shutting down TCP server: {}", e);
        }

        // Shutdown serial connection
        if let Err(e) = self.serial_handle.shutdown().await {
            error!("Error shutting down serial connection: {}", e);
        }

        // Process remaining messages in queue
        if let Err(e) = self.process_message_queue().await {
            error!("Error processing final messages: {}", e);
        }

        info!("Data forwarder shutdown complete");
        Ok(())
    }
}

/// Example event handler for logging
pub struct LoggingEventHandler;

impl EventHandler for LoggingEventHandler {
    async fn handle(&self, data: &[u8]) -> ForwarderResult<()> {
        debug!("Event handler received {} bytes: {:?}", data.len(),
               String::from_utf8_lossy(&data[..std::cmp::min(data.len(), 50)]));
        Ok(())
    }
}

/// Example event handler for data transformation
pub struct DataTransformHandler {
    transform_fn: Box<dyn Fn(&[u8]) -> Vec<u8> + Send + Sync>,
}

impl DataTransformHandler {
    pub fn new<F>(transform_fn: F) -> Self
    where
        F: Fn(&[u8]) -> Vec<u8> + Send + Sync + 'static,
    {
        Self {
            transform_fn: Box::new(transform_fn),
        }
    }
}

impl EventHandler for DataTransformHandler {
    async fn handle(&self, data: &[u8]) -> ForwarderResult<()> {
        let transformed = (self.transform_fn)(data);
        debug!("Transformed {} bytes to {} bytes", data.len(), transformed.len());
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{SerialConfig, TcpConfig};
    use crate::serial_manager::SerialManager;
    use crate::tcp_server::TcpServer;
    use std::time::Duration;

    fn create_test_serial_config() -> SerialConfig {
        SerialConfig {
            port: "COM1".to_string(),
            baud_rate: 9600,
            data_bits: serialport::DataBits::Eight,
            stop_bits: serialport::StopBits::One,
            parity: serialport::Parity::None,
            timeout: Duration::from_millis(100),
        }
    }

    fn create_test_tcp_config() -> TcpConfig {
        TcpConfig {
            host: "127.0.0.1".to_string(),
            port: 0,
            max_clients: 5,
            client_timeout: Duration::from_secs(30),
            buffer_size: 1024,
        }
    }

    #[tokio::test]
    async fn test_message_queue() {
        let mut queue = MessageQueue::new(2);

        // Test serial to TCP queue
        assert!(queue.push_serial_to_tcp(vec![1, 2, 3]).is_ok());
        assert!(queue.push_serial_to_tcp(vec![4, 5, 6]).is_ok());
        assert!(queue.push_serial_to_tcp(vec![7, 8, 9]).is_err()); // Queue full

        assert_eq!(queue.pop_serial_to_tcp(), Some(vec![1, 2, 3]));
        assert_eq!(queue.pop_serial_to_tcp(), Some(vec![4, 5, 6]));
        assert_eq!(queue.pop_serial_to_tcp(), None);

        // Test TCP to serial queue
        let client_id = ClientId::new();
        assert!(queue.push_tcp_to_serial(client_id, vec![1, 2, 3]).is_ok());
        assert_eq!(queue.pop_tcp_to_serial(), Some((client_id, vec![1, 2, 3])));
    }

    #[tokio::test]
    async fn test_data_forwarder_creation() {
        let serial_config = create_test_serial_config();
        let tcp_config = create_test_tcp_config();

        let (_serial_manager, serial_handle) = SerialManager::new(serial_config);
        let (_tcp_server, tcp_handle) = TcpServer::new(tcp_config);

        let forwarder = DataForwarder::new(serial_handle, tcp_handle);

        assert!(!forwarder.is_running);

        let stats = forwarder.get_statistics().await;
        assert_eq!(stats.bytes_serial_to_tcp, 0);
        assert_eq!(stats.bytes_tcp_to_serial, 0);
        assert_eq!(stats.active_clients, 0);
    }

    #[tokio::test]
    async fn test_event_dispatcher() {
        let mut dispatcher = EventDispatcher::new();
        let handler = Box::new(LoggingEventHandler);

        dispatcher.register_handler("test".to_string(), handler);

        let result = dispatcher.dispatch("test", b"hello").await;
        assert!(result.is_ok());

        let result = dispatcher.dispatch("unknown", b"hello").await;
        assert!(result.is_ok()); // Should not fail for unknown event types
    }
}
