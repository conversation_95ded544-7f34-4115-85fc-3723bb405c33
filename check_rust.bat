@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo         Rust Environment Checker
echo ========================================
echo.

set "all_ok=1"

:: 检查 rustc
echo [1/4] Checking rustc...
where rustc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ rustc found
    rustc --version
) else (
    echo ❌ rustc not found
    set "all_ok=0"
)
echo.

:: 检查 cargo
echo [2/4] Checking cargo...
where cargo >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ cargo found
    cargo --version
) else (
    echo ❌ cargo not found
    set "all_ok=0"
)
echo.

:: 检查 rustup
echo [3/4] Checking rustup...
where rustup >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ rustup found
    rustup --version
) else (
    echo ❌ rustup not found
    set "all_ok=0"
)
echo.

:: 检查环境变量
echo [4/4] Checking environment...
if defined CARGO_HOME (
    echo ✅ CARGO_HOME: %CARGO_HOME%
) else (
    echo ⚠️  CARGO_HOME not set (using default)
)

if defined RUSTUP_HOME (
    echo ✅ RUSTUP_HOME: %RUSTUP_HOME%
) else (
    echo ⚠️  RUSTUP_HOME not set (using default)
)

:: 检查 PATH
echo %PATH% | findstr /i "\.cargo\bin" >nul
if %errorlevel% equ 0 (
    echo ✅ Cargo bin directory in PATH
) else (
    echo ❌ Cargo bin directory not in PATH
    set "all_ok=0"
)
echo.

:: 总结
echo ========================================
if "%all_ok%"=="1" (
    echo 🎉 Rust environment is ready!
    echo.
    echo You can now:
    echo   1. Run 'build.bat' to compile COM2TCP
    echo   2. Run 'cargo build --release' manually
    echo   3. Start developing with Rust
    echo.
) else (
    echo ❌ Rust environment has issues!
    echo.
    echo 📥 To install Rust:
    echo   1. See INSTALL_RUST.md for detailed instructions
    echo   2. Visit https://rustup.rs/ to download installer
    echo   3. Run rustup-init.exe and follow instructions
    echo   4. Restart your command prompt
    echo   5. Run this script again
    echo.
    echo 🔧 If Rust is installed but not working:
    echo   1. Restart your command prompt
    echo   2. Check if %%USERPROFILE%%\.cargo\bin is in PATH
    echo   3. Try running: rustup default stable
    echo.
)

pause
