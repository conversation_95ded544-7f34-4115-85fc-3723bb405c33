@echo off

echo.
echo Building COM2TCP...
echo.

where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Cargo not found. Please install Rust first.
    echo Run: simple_check.bat
    pause
    exit /b 1
)

if not exist "Cargo.toml" (
    echo ERROR: Cargo.toml not found
    echo Please run this script from the project directory
    pause
    exit /b 1
)

echo Step 1: Checking code format...
cargo fmt --check >nul 2>&1
if %errorlevel% neq 0 (
    echo Formatting code...
    cargo fmt
)

echo Step 2: Running tests...
cargo test
if %errorlevel% neq 0 (
    echo ERROR: Tests failed
    pause
    exit /b 1
)

echo Step 3: Building release version...
cargo build --release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

if exist "target\release\com2tcp.exe" (
    echo.
    echo SUCCESS: Build completed!
    echo.
    echo Executable: target\release\com2tcp.exe
    echo.
    echo Try:
    echo   target\release\com2tcp.exe --help
    echo   target\release\com2tcp.exe --list-ports
    echo.
) else (
    echo ERROR: Executable not found
    exit /b 1
)

pause
