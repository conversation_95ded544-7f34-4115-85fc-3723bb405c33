========================================
         COM2TCP - 快速开始指南
========================================

欢迎使用 COM2TCP！这是一个高性能的串口到TCP转发器。

第一次使用？请按照以下步骤操作：

步骤 1: 安装 Rust 环境
========================================
如果您还没有安装 Rust：

方法A（推荐）：
  双击运行 install_rust.bat

方法B（手动）：
  1. 访问 https://rustup.rs/
  2. 下载 rustup-init.exe
  3. 运行安装程序
  4. 重启命令提示符

步骤 2: 验证环境
========================================
双击运行 check_rust.bat

如果看到 "SUCCESS: Rust environment is ready!"
说明环境配置正确，可以继续下一步。

步骤 3: 编译项目
========================================
双击运行 build.bat

等待编译完成，您会看到：
"SUCCESS: Build completed successfully!"

步骤 4: 开始使用
========================================
双击运行 quick_start.bat

这会打开一个菜单，您可以：
1. 查看可用串口
2. 使用默认设置启动
3. 自定义配置

步骤 5: 连接测试
========================================
启动 COM2TCP 后，您可以使用以下方式连接：

1. 使用 telnet：
   打开新的命令提示符，输入：
   telnet localhost 8080

2. 使用其他TCP客户端工具

常见问题
========================================
Q: 提示找不到串口？
A: 运行 quick_start.bat，选择"1. List available serial ports"

Q: 编译失败？
A: 确保已正确安装 Rust，运行 check_rust.bat 检查

Q: 连接不上TCP端口？
A: 检查防火墙设置，确保端口8080未被占用

Q: 需要帮助？
A: 查看 docs 文件夹中的详细文档

文件说明
========================================
install_rust.bat    - Rust 安装助手
check_rust.bat      - 环境检查工具
build.bat           - 项目编译脚本
quick_start.bat     - 快速开始菜单

docs/               - 详细文档
  user_manual.md    - 用户手册
  usage_examples.md - 使用示例
  troubleshooting.md - 故障排除

更多信息
========================================
项目主页：
https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git

如有问题，请查看文档或提交 Issue。

祝您使用愉快！
