//! COM2TCP - Serial port to TCP forwarder
//! 
//! This library provides functionality to create a bidirectional bridge between
//! a serial port and TCP connections, allowing multiple TCP clients to communicate
//! with a single serial device.

pub mod cli;
pub mod config;
pub mod data_forwarder;
pub mod error;
pub mod serial_manager;
pub mod tcp_server;

// Re-export commonly used types
pub use config::Config;
pub use error::{Com2TcpError, Result};
pub use data_forwarder::DataForwarder;
pub use serial_manager::{SerialManager, SerialHandle, SerialEvent, SerialCommand};
pub use tcp_server::{TcpServer, TcpHandle, TcpEvent, TcpCommand, ClientId};

/// Application version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Application name
pub const APP_NAME: &str = env!("CARGO_PKG_NAME");

/// Application description
pub const APP_DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(!APP_NAME.is_empty());
        assert!(!APP_DESCRIPTION.is_empty());
    }
}
