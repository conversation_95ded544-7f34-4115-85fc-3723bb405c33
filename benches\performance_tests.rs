use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use com2tcp::config::{Config, SerialConfig, TcpConfig, LogConfig, LimitsConfig};
use com2tcp::data_forwarder::{<PERSON>Forwarder, MessageQueue};
use com2tcp::serial_manager::SerialManager;
use com2tcp::tcp_server::{TcpServer, ClientId};
use std::time::Duration;
use tokio::runtime::Runtime;

fn create_test_config() -> Config {
    Config {
        serial: SerialConfig {
            port: "COM99".to_string(),
            baud_rate: 115200,
            data_bits: serialport::DataBits::Eight,
            stop_bits: serialport::StopBits::One,
            parity: serialport::Parity::None,
            timeout: Duration::from_millis(10),
        },
        tcp: TcpConfig {
            host: "127.0.0.1".to_string(),
            port: 0,
            max_clients: 100,
            client_timeout: Duration::from_secs(30),
            buffer_size: 4096,
        },
        logging: LogConfig {
            level: log::LevelFilter::Error, // Minimal logging for benchmarks
            file: None,
        },
        limits: LimitsConfig {
            max_message_size: 1024 * 1024,
            max_queue_size: 10000,
        },
    }
}

fn bench_message_queue_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("message_queue");
    
    for queue_size in [100, 1000, 10000].iter() {
        group.bench_with_input(
            BenchmarkId::new("push_serial_to_tcp", queue_size),
            queue_size,
            |b, &size| {
                b.iter(|| {
                    let mut queue = MessageQueue::new(size);
                    for i in 0..size {
                        let data = vec![i as u8; 64]; // 64 bytes per message
                        let _ = queue.push_serial_to_tcp(black_box(data));
                    }
                });
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("push_tcp_to_serial", queue_size),
            queue_size,
            |b, &size| {
                b.iter(|| {
                    let mut queue = MessageQueue::new(size);
                    let client_id = ClientId::new();
                    for i in 0..size {
                        let data = vec![i as u8; 64];
                        let _ = queue.push_tcp_to_serial(black_box(client_id), black_box(data));
                    }
                });
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("pop_operations", queue_size),
            queue_size,
            |b, &size| {
                b.iter(|| {
                    let mut queue = MessageQueue::new(size);
                    let client_id = ClientId::new();
                    
                    // Fill queue
                    for i in 0..size/2 {
                        let data = vec![i as u8; 64];
                        let _ = queue.push_serial_to_tcp(data.clone());
                        let _ = queue.push_tcp_to_serial(client_id, data);
                    }
                    
                    // Pop all items
                    while queue.pop_serial_to_tcp().is_some() {}
                    while queue.pop_tcp_to_serial().is_some() {}
                });
            },
        );
    }
    
    group.finish();
}

fn bench_client_id_generation(c: &mut Criterion) {
    c.bench_function("client_id_generation", |b| {
        b.iter(|| {
            for _ in 0..1000 {
                let _id = ClientId::new();
            }
        });
    });
}

fn bench_data_forwarder_creation(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("data_forwarder_creation", |b| {
        b.iter(|| {
            rt.block_on(async {
                let config = create_test_config();
                let (_serial_manager, serial_handle) = SerialManager::new(config.serial);
                let (_tcp_server, tcp_handle) = TcpServer::new(config.tcp);
                let _forwarder = DataForwarder::new(black_box(serial_handle), black_box(tcp_handle));
            });
        });
    });
}

fn bench_statistics_operations(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("get_statistics", |b| {
        let config = create_test_config();
        let (_serial_manager, serial_handle) = SerialManager::new(config.serial);
        let (_tcp_server, tcp_handle) = TcpServer::new(config.tcp);
        let forwarder = DataForwarder::new(serial_handle, tcp_handle);
        
        b.iter(|| {
            rt.block_on(async {
                let _stats = forwarder.get_statistics().await;
            });
        });
    });
}

fn bench_config_validation(c: &mut Criterion) {
    c.bench_function("config_validation", |b| {
        let config = create_test_config();
        b.iter(|| {
            let _ = black_box(config.clone()).validate();
        });
    });
}

fn bench_error_creation(c: &mut Criterion) {
    use com2tcp::error::{SerialError, TcpError, ForwarderError, Com2TcpError};
    
    let mut group = c.benchmark_group("error_creation");
    
    group.bench_function("serial_error", |b| {
        b.iter(|| {
            let _error = SerialError::port_not_available(black_box("COM1"));
        });
    });
    
    group.bench_function("tcp_error", |b| {
        b.iter(|| {
            let _error = TcpError::max_clients_exceeded(black_box(10));
        });
    });
    
    group.bench_function("forwarder_error", |b| {
        b.iter(|| {
            let _error = ForwarderError::queue_full(black_box(1000));
        });
    });
    
    group.bench_function("error_conversion", |b| {
        b.iter(|| {
            let serial_error = SerialError::port_not_available("COM1");
            let _com_error: Com2TcpError = black_box(serial_error).into();
        });
    });
    
    group.finish();
}

fn bench_data_processing(c: &mut Criterion) {
    let mut group = c.benchmark_group("data_processing");
    
    for data_size in [64, 512, 1024, 4096, 8192].iter() {
        group.bench_with_input(
            BenchmarkId::new("data_clone", data_size),
            data_size,
            |b, &size| {
                let data = vec![0u8; size];
                b.iter(|| {
                    let _cloned = black_box(data.clone());
                });
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("data_to_string", data_size),
            data_size,
            |b, &size| {
                let data = vec![65u8; size]; // ASCII 'A'
                b.iter(|| {
                    let _string = String::from_utf8_lossy(black_box(&data));
                });
            },
        );
    }
    
    group.finish();
}

fn bench_concurrent_operations(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    c.bench_function("concurrent_queue_access", |b| {
        b.iter(|| {
            rt.block_on(async {
                use tokio::sync::Mutex;
                use std::sync::Arc;
                
                let queue = Arc::new(Mutex::new(MessageQueue::new(1000)));
                let mut handles = vec![];
                
                // Spawn multiple tasks that access the queue concurrently
                for i in 0..10 {
                    let queue_clone = Arc::clone(&queue);
                    let handle = tokio::spawn(async move {
                        for j in 0..100 {
                            let data = vec![(i * 100 + j) as u8; 32];
                            let mut q = queue_clone.lock().await;
                            let _ = q.push_serial_to_tcp(data);
                        }
                    });
                    handles.push(handle);
                }
                
                // Wait for all tasks to complete
                for handle in handles {
                    let _ = handle.await;
                }
            });
        });
    });
}

fn bench_memory_usage(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_usage");
    
    group.bench_function("large_message_queue", |b| {
        b.iter(|| {
            let mut queue = MessageQueue::new(10000);
            
            // Fill queue with large messages
            for i in 0..1000 {
                let data = vec![i as u8; 1024]; // 1KB per message
                let _ = queue.push_serial_to_tcp(black_box(data));
            }
            
            // Process all messages
            while queue.pop_serial_to_tcp().is_some() {}
        });
    });
    
    group.bench_function("many_small_messages", |b| {
        b.iter(|| {
            let mut queue = MessageQueue::new(10000);
            
            // Fill queue with many small messages
            for i in 0..5000 {
                let data = vec![i as u8; 16]; // 16 bytes per message
                let _ = queue.push_serial_to_tcp(black_box(data));
            }
            
            // Process all messages
            while queue.pop_serial_to_tcp().is_some() {}
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    bench_message_queue_operations,
    bench_client_id_generation,
    bench_data_forwarder_creation,
    bench_statistics_operations,
    bench_config_validation,
    bench_error_creation,
    bench_data_processing,
    bench_concurrent_operations,
    bench_memory_usage
);

criterion_main!(benches);
