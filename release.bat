@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo       COM2TCP Release Package Creator
echo ========================================
echo.

:: 获取版本号
for /f "tokens=3" %%a in ('findstr "version" Cargo.toml ^| head -1') do (
    set version=%%a
    set version=!version:"=!
)

echo 📦 Creating release package for COM2TCP v!version!
echo.

:: 检查是否已构建
if not exist "target\release\com2tcp.exe" (
    echo ❌ Release build not found. Running build first...
    call build.bat
    if %errorlevel% neq 0 (
        echo ❌ Build failed. Cannot create release package.
        pause
        exit /b 1
    )
)

:: 创建发布目录
set release_dir=com2tcp-v!version!-windows
if exist "!release_dir!" (
    echo 🗑️  Removing existing release directory...
    rmdir /s /q "!release_dir!"
)

echo 📁 Creating release directory: !release_dir!
mkdir "!release_dir!"
mkdir "!release_dir!\docs"
mkdir "!release_dir!\examples"

:: 复制可执行文件
echo 📋 Copying executable...
copy "target\release\com2tcp.exe" "!release_dir!\"

:: 复制文档
echo 📋 Copying documentation...
copy "README.md" "!release_dir!\"
copy "LICENSE" "!release_dir!\"
copy "CHANGELOG.md" "!release_dir!\"
copy "docs\user_manual.md" "!release_dir!\docs\"
copy "docs\usage_examples.md" "!release_dir!\docs\"
copy "docs\troubleshooting.md" "!release_dir!\docs\"

:: 创建示例配置文件
echo 📋 Creating example configuration...
echo # COM2TCP Configuration Example > "!release_dir!\examples\config.toml"
echo # Copy this file and modify as needed >> "!release_dir!\examples\config.toml"
echo. >> "!release_dir!\examples\config.toml"
echo [serial] >> "!release_dir!\examples\config.toml"
echo port = "COM1" >> "!release_dir!\examples\config.toml"
echo baud_rate = 9600 >> "!release_dir!\examples\config.toml"
echo data_bits = 8 >> "!release_dir!\examples\config.toml"
echo stop_bits = 1 >> "!release_dir!\examples\config.toml"
echo parity = "None" >> "!release_dir!\examples\config.toml"
echo timeout = 1000 >> "!release_dir!\examples\config.toml"
echo. >> "!release_dir!\examples\config.toml"
echo [tcp] >> "!release_dir!\examples\config.toml"
echo host = "0.0.0.0" >> "!release_dir!\examples\config.toml"
echo port = 8080 >> "!release_dir!\examples\config.toml"
echo max_clients = 10 >> "!release_dir!\examples\config.toml"
echo client_timeout = 300000 >> "!release_dir!\examples\config.toml"
echo buffer_size = 1024 >> "!release_dir!\examples\config.toml"
echo. >> "!release_dir!\examples\config.toml"
echo [logging] >> "!release_dir!\examples\config.toml"
echo level = "info" >> "!release_dir!\examples\config.toml"
echo file = "com2tcp.log" >> "!release_dir!\examples\config.toml"

:: 创建快速启动脚本
echo 📋 Creating quick start scripts...
echo @echo off > "!release_dir!\start_com1.bat"
echo echo Starting COM2TCP on COM1... >> "!release_dir!\start_com1.bat"
echo com2tcp.exe --port COM1 --baud 9600 --tcp-port 8080 --verbose >> "!release_dir!\start_com1.bat"
echo pause >> "!release_dir!\start_com1.bat"

echo @echo off > "!release_dir!\list_ports.bat"
echo echo Listing available serial ports... >> "!release_dir!\list_ports.bat"
echo com2tcp.exe --list-ports >> "!release_dir!\list_ports.bat"
echo pause >> "!release_dir!\list_ports.bat"

:: 创建README文件
echo 📋 Creating release README...
echo COM2TCP v!version! - Windows Release > "!release_dir!\README_RELEASE.txt"
echo ================================== >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo This package contains: >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo Files: >> "!release_dir!\README_RELEASE.txt"
echo   com2tcp.exe           - Main executable >> "!release_dir!\README_RELEASE.txt"
echo   README.md             - Project documentation >> "!release_dir!\README_RELEASE.txt"
echo   LICENSE               - MIT license >> "!release_dir!\README_RELEASE.txt"
echo   CHANGELOG.md          - Version history >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo Quick Start Scripts: >> "!release_dir!\README_RELEASE.txt"
echo   start_com1.bat        - Start with COM1 default settings >> "!release_dir!\README_RELEASE.txt"
echo   list_ports.bat        - List available serial ports >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo Documentation: >> "!release_dir!\README_RELEASE.txt"
echo   docs\user_manual.md   - Complete user manual >> "!release_dir!\README_RELEASE.txt"
echo   docs\usage_examples.md - Usage examples >> "!release_dir!\README_RELEASE.txt"
echo   docs\troubleshooting.md - Troubleshooting guide >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo Examples: >> "!release_dir!\README_RELEASE.txt"
echo   examples\config.toml  - Configuration file example >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo Quick Start: >> "!release_dir!\README_RELEASE.txt"
echo   1. Double-click list_ports.bat to see available ports >> "!release_dir!\README_RELEASE.txt"
echo   2. Edit start_com1.bat to use your serial port >> "!release_dir!\README_RELEASE.txt"
echo   3. Double-click start_com1.bat to start the server >> "!release_dir!\README_RELEASE.txt"
echo   4. Connect with: telnet localhost 8080 >> "!release_dir!\README_RELEASE.txt"
echo. >> "!release_dir!\README_RELEASE.txt"
echo For more information, see README.md >> "!release_dir!\README_RELEASE.txt"

:: 创建压缩包（如果有7zip）
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    echo 📦 Creating ZIP archive...
    7z a -tzip "!release_dir!.zip" "!release_dir!\*"
    if %errorlevel% equ 0 (
        echo ✅ ZIP archive created: !release_dir!.zip
    ) else (
        echo ⚠️  Failed to create ZIP archive
    )
) else (
    echo ⚠️  7-Zip not found. ZIP archive not created.
    echo    You can manually compress the !release_dir! folder.
)

echo.
echo 🎉 Release package created successfully!
echo.
echo 📍 Release directory: !release_dir!\
echo 📦 Contents:
dir /b "!release_dir!"
echo.
echo 🚀 Ready for distribution!
echo.

pause
