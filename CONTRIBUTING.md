# 贡献指南

感谢您对 COM2TCP 项目的关注！我们欢迎各种形式的贡献，包括但不限于：

- 报告 Bug
- 提出新功能建议
- 提交代码改进
- 完善文档
- 分享使用经验

## 如何贡献

### 报告问题

如果您发现了 Bug 或有功能建议，请：

1. 首先搜索现有的 [Issues](https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git/issues) 确认问题未被报告
2. 创建新的 Issue，请包含：
   - 清晰的问题描述
   - 复现步骤
   - 预期行为和实际行为
   - 环境信息（操作系统、Rust版本等）
   - 相关日志或错误信息

### 提交代码

1. **Fork 项目**
   ```bash
   git clone https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp.git
   cd com2tcp
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **进行开发**
   - 遵循现有的代码风格
   - 添加必要的测试
   - 更新相关文档

4. **运行测试**
   ```bash
   cargo test
   cargo clippy
   cargo fmt --check
   ```

5. **提交更改**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   ```

6. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **创建 Pull Request**
   - 在 Coding 平台创建 Pull Request
   - 详细描述您的更改
   - 关联相关的 Issue

## 开发环境设置

### 系统要求

- Rust 1.70 或更高版本
- Git
- 支持的操作系统：Windows、Linux、macOS

### 安装依赖

```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 克隆项目
git clone https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp.git
cd com2tcp

# 构建项目
cargo build

# 运行测试
cargo test
```

### 开发工具推荐

- **IDE**: VS Code + Rust Analyzer 插件
- **调试**: rust-gdb 或 VS Code 调试器
- **格式化**: rustfmt
- **静态分析**: clippy

## 代码规范

### Rust 代码风格

- 使用 `rustfmt` 格式化代码
- 遵循 Rust 官方命名约定
- 使用 `clippy` 进行静态分析
- 添加适当的文档注释

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型包括：
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(serial): 添加自动重连功能

当串口连接断开时，自动尝试重新连接，
最多重试5次，每次间隔递增。

Closes #123
```

## 测试指南

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test serial_manager

# 运行集成测试
cargo test --test integration_tests

# 运行基准测试
cargo bench
```

### 编写测试

- 为新功能添加单元测试
- 为重要功能添加集成测试
- 确保测试覆盖率不降低
- 测试应该快速、可靠、独立

### 测试命名约定

```rust
#[test]
fn test_function_name_should_behavior_when_condition() {
    // 测试代码
}
```

## 文档贡献

### 文档类型

- **用户文档**: 使用说明、配置指南
- **开发文档**: API 文档、架构说明
- **示例代码**: 使用示例、最佳实践

### 文档规范

- 使用清晰、简洁的语言
- 提供实际的代码示例
- 保持文档与代码同步
- 支持中英文双语

## 发布流程

### 版本号规范

使用 [Semantic Versioning](https://semver.org/)：

- `MAJOR.MINOR.PATCH`
- MAJOR: 不兼容的 API 更改
- MINOR: 向后兼容的功能添加
- PATCH: 向后兼容的 Bug 修复

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG 已更新
- [ ] 版本号已更新
- [ ] 创建 Git 标签
- [ ] 发布到 crates.io（如适用）

## 社区行为准则

### 我们的承诺

为了营造一个开放和友好的环境，我们承诺：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 不可接受的行为

- 使用性别化语言或图像，以及不受欢迎的性关注或性骚扰
- 恶意评论、人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人的私人信息
- 在专业环境中可能被认为不合适的其他行为

## 获得帮助

如果您需要帮助或有疑问：

1. 查看 [文档](docs/)
2. 搜索现有的 [Issues](https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git/issues)
3. 创建新的 Issue 提问
4. 参与 [Discussions](https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git/discussions)（如果可用）

## 致谢

感谢所有为 COM2TCP 项目做出贡献的开发者！

### 主要贡献者

- 项目维护者和核心开发团队
- 文档贡献者
- Bug 报告者和测试者
- 功能建议者

### 特别感谢

- Rust 社区提供的优秀工具和库
- 所有提供反馈和建议的用户
- 开源社区的支持和鼓励

---

再次感谢您的贡献！让我们一起让 COM2TCP 变得更好！
