@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo         COM2TCP Quick Start
echo ========================================
echo.

:: 检查是否已编译
if exist "target\release\com2tcp.exe" (
    echo [OK] COM2TCP executable found
    goto :menu
) else (
    echo [INFO] COM2TCP executable not found
    echo.
    echo Building COM2TCP first...
    call build.bat
    if %errorlevel% neq 0 (
        echo [ERROR] Build failed. Please check the errors above.
        pause
        exit /b 1
    )
)

:menu
echo.
echo What would you like to do?
echo.
echo 1. List available serial ports
echo 2. Start with COM1 (9600 baud, port 8080)
echo 3. Start with COM2 (9600 baud, port 8080)
echo 4. Start with custom settings
echo 5. Generate config file
echo 6. Show help
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto :list_ports
if "%choice%"=="2" goto :start_com1
if "%choice%"=="3" goto :start_com2
if "%choice%"=="4" goto :custom_start
if "%choice%"=="5" goto :generate_config
if "%choice%"=="6" goto :show_help
if "%choice%"=="7" goto :exit
echo Invalid choice. Please try again.
goto :menu

:list_ports
echo.
echo Available serial ports:
echo ========================================
target\release\com2tcp.exe --list-ports
echo ========================================
echo.
pause
goto :menu

:start_com1
echo.
echo Starting COM2TCP on COM1...
echo ========================================
echo Serial: COM1, 9600 baud
echo TCP: localhost:8080
echo Max clients: 10
echo.
echo Press Ctrl+C to stop
echo ========================================
target\release\com2tcp.exe --port COM1 --baud 9600 --tcp-port 8080 --verbose
goto :menu

:start_com2
echo.
echo Starting COM2TCP on COM2...
echo ========================================
echo Serial: COM2, 9600 baud
echo TCP: localhost:8080
echo Max clients: 10
echo.
echo Press Ctrl+C to stop
echo ========================================
target\release\com2tcp.exe --port COM2 --baud 9600 --tcp-port 8080 --verbose
goto :menu

:custom_start
echo.
echo Custom Configuration
echo ========================================
set /p port="Enter serial port (e.g., COM1): "
set /p baud="Enter baud rate (e.g., 9600): "
set /p tcp_port="Enter TCP port (e.g., 8080): "
set /p max_clients="Enter max clients (e.g., 10): "

echo.
echo Starting COM2TCP with custom settings...
echo ========================================
echo Serial: %port%, %baud% baud
echo TCP: localhost:%tcp_port%
echo Max clients: %max_clients%
echo.
echo Press Ctrl+C to stop
echo ========================================
target\release\com2tcp.exe --port %port% --baud %baud% --tcp-port %tcp_port% --max-clients %max_clients% --verbose
goto :menu

:generate_config
echo.
echo Generating configuration file...
target\release\com2tcp.exe --generate-config config.toml
if %errorlevel% equ 0 (
    echo [OK] Configuration file created: config.toml
    echo.
    echo You can now:
    echo 1. Edit config.toml with your settings
    echo 2. Run: target\release\com2tcp.exe --config config.toml
) else (
    echo [ERROR] Failed to generate configuration file
)
echo.
pause
goto :menu

:show_help
echo.
echo COM2TCP Help
echo ========================================
target\release\com2tcp.exe --help
echo ========================================
echo.
echo For more information:
echo   - docs\user_manual.md - Complete user manual
echo   - docs\usage_examples.md - Usage examples
echo   - docs\troubleshooting.md - Troubleshooting guide
echo.
pause
goto :menu

:exit
echo.
echo Thank you for using COM2TCP!
echo.
echo Tips:
echo   - Use 'telnet localhost 8080' to connect as a client
echo   - Check docs\ folder for detailed documentation
echo   - Visit https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git for updates
echo.
pause
exit /b 0
