@echo off
echo COM2TCP Build Script
echo ==================

echo Checking Rust installation...
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Cargo not found. Please install Rust from https://rustup.rs/
    echo.
    echo To install Rust:
    echo 1. Download and run rustup-init.exe from https://rustup.rs/
    echo 2. Follow the installation instructions
    echo 3. Restart your command prompt
    echo 4. Run this script again
    pause
    exit /b 1
)

echo Rust version:
rustc --version
echo.

echo Cargo version:
cargo --version
echo.

echo Checking project structure...
if not exist "Cargo.toml" (
    echo Error: Cargo.toml not found
    exit /b 1
)

if not exist "src\main.rs" (
    echo Error: src\main.rs not found
    exit /b 1
)

echo Project structure OK
echo.

echo Running cargo check...
cargo check

if %errorlevel% neq 0 (
    echo.
    echo Build check failed. Please fix the errors above.
    pause
    exit /b 1
)

echo.
echo Build check successful!
echo.

echo To build the project:
echo   cargo build
echo.
echo To build for release:
echo   cargo build --release
echo.
echo To run the project:
echo   cargo run -- --help
echo.

pause
