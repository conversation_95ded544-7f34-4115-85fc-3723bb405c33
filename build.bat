@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo       COM2TCP Windows Build Script
echo ========================================
echo.

:: 检查Rust安装
echo [1/6] Checking Rust installation...
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Cargo not found. Please install Rust first.
    echo.
    echo 📥 To install Rust:
    echo    1. Visit https://rustup.rs/
    echo    2. Download and run rustup-init.exe
    echo    3. Follow the installation instructions
    echo    4. Restart your command prompt
    echo    5. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ Rust found
rustc --version
cargo --version
echo.

:: 检查项目结构
echo [2/6] Checking project structure...
if not exist "Cargo.toml" (
    echo ❌ Error: Cargo.toml not found
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "src\main.rs" (
    echo ❌ Error: src\main.rs not found
    echo Please ensure the project structure is correct.
    pause
    exit /b 1
)

echo ✅ Project structure OK
echo.

:: 运行代码检查
echo [3/6] Running code checks...
echo Checking code format...
cargo fmt --check >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Code formatting issues found. Running auto-format...
    cargo fmt
    echo ✅ Code formatted
) else (
    echo ✅ Code format OK
)

echo Checking with clippy...
cargo clippy --all-targets --all-features -- -D warnings
if %errorlevel% neq 0 (
    echo ❌ Clippy found issues. Please fix them before building.
    pause
    exit /b 1
)
echo ✅ Clippy checks passed
echo.

:: 运行测试
echo [4/6] Running tests...
cargo test
if %errorlevel% neq 0 (
    echo ❌ Tests failed. Please fix them before building.
    pause
    exit /b 1
)
echo ✅ All tests passed
echo.

:: 构建发布版本
echo [5/6] Building release version...
cargo build --release
if %errorlevel% neq 0 (
    echo ❌ Build failed. Please check the errors above.
    pause
    exit /b 1
)
echo ✅ Build successful
echo.

:: 检查输出文件
echo [6/6] Checking output...
if exist "target\release\com2tcp.exe" (
    echo ✅ Executable created: target\release\com2tcp.exe

    :: 获取文件大小
    for %%A in ("target\release\com2tcp.exe") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo 📦 File size: !size_mb! MB
    )

    echo.
    echo 🎉 Build completed successfully!
    echo.
    echo 📍 Executable location: target\release\com2tcp.exe
    echo.
    echo 🚀 Quick start:
    echo    target\release\com2tcp.exe --help
    echo    target\release\com2tcp.exe --list-ports
    echo    target\release\com2tcp.exe --port COM1 --baud 9600 --tcp-port 8080
    echo.
    echo 📚 For more information, see docs\user_manual.md
    echo.
) else (
    echo ❌ Error: Executable not found after build
    exit /b 1
)

pause
