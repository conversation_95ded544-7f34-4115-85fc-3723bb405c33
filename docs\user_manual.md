# COM2TCP 用户手册

## 目录

1. [简介](#简介)
2. [安装](#安装)
3. [快速开始](#快速开始)
4. [命令行参数](#命令行参数)
5. [配置文件](#配置文件)
6. [使用场景](#使用场景)
7. [客户端连接](#客户端连接)
8. [监控和日志](#监控和日志)
9. [性能优化](#性能优化)
10. [故障排除](#故障排除)

## 简介

COM2TCP 是一个高性能的串口到 TCP 双向数据转发器，允许多个 TCP 客户端同时连接到一个串口设备。它提供了以下主要功能：

- **双向数据转发**：串口数据实时转发到所有 TCP 客户端，TCP 客户端数据转发到串口
- **多客户端支持**：支持多个 TCP 客户端同时连接
- **数据同步**：所有客户端都能看到其他客户端发送的数据
- **高性能**：基于 Rust 和 Tokio 异步运行时，支持高并发
- **跨平台**：支持 Windows、Linux 和 macOS
- **灵活配置**：支持命令行参数和配置文件

## 安装

### 系统要求

- **操作系统**：Windows 10+, Linux (Ubuntu 20.04+), macOS 11+
- **内存**：至少 50MB 可用内存
- **网络**：TCP 端口访问权限

### 安装方法

#### 方法 1：从源码编译

1. **安装 Rust**：
   ```bash
   # 访问 https://rustup.rs/ 下载并安装 Rust
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **克隆项目**：
   ```bash
   git clone https://github.com/yourusername/com2tcp.git
   cd com2tcp
   ```

3. **编译项目**：
   ```bash
   cargo build --release
   ```

4. **安装到系统**：
   ```bash
   cargo install --path .
   ```

#### 方法 2：下载预编译版本

从 [Releases](https://github.com/yourusername/com2tcp/releases) 页面下载对应平台的预编译版本。

### 验证安装

```bash
com2tcp --version
com2tcp --help
```

## 快速开始

### 1. 基本使用

```bash
# Windows
com2tcp --port COM1 --baud 9600 --tcp-port 8080

# Linux
com2tcp --port /dev/ttyUSB0 --baud 9600 --tcp-port 8080

# macOS
com2tcp --port /dev/cu.usbserial-* --baud 9600 --tcp-port 8080
```

### 2. 查看可用串口

```bash
com2tcp --list-ports
```

### 3. 连接测试

启动程序后，使用 telnet 测试连接：

```bash
telnet localhost 8080
```

## 命令行参数

### 基本参数

| 参数 | 短参数 | 默认值 | 说明 |
|------|--------|--------|------|
| `--port` | `-p` | COM1 | 串口设备路径 |
| `--baud` | `-b` | 9600 | 波特率 |
| `--tcp-port` | `-t` | 8080 | TCP 监听端口 |
| `--tcp-host` | | 0.0.0.0 | TCP 绑定地址 |

### 串口参数

| 参数 | 短参数 | 默认值 | 说明 |
|------|--------|--------|------|
| `--data-bits` | `-d` | 8 | 数据位 (5,6,7,8) |
| `--stop-bits` | `-s` | 1 | 停止位 (1,2) |
| `--parity` | | none | 校验位 (none,odd,even) |
| `--timeout` | | 1000 | 串口超时 (毫秒) |

### 网络参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--max-clients` | 10 | 最大客户端连接数 |
| `--client-timeout` | 300 | 客户端超时 (秒) |
| `--buffer-size` | 1024 | 缓冲区大小 (字节) |

### 日志参数

| 参数 | 短参数 | 默认值 | 说明 |
|------|--------|--------|------|
| `--log-level` | | info | 日志级别 |
| `--log-file` | | | 日志文件路径 |
| `--verbose` | `-v` | | 详细输出 |

### 其他参数

| 参数 | 说明 |
|------|------|
| `--config` | 配置文件路径 |
| `--generate-config` | 生成默认配置文件 |
| `--list-ports` | 列出可用串口 |
| `--dry-run` | 验证配置但不启动 |
| `--help` | 显示帮助信息 |
| `--version` | 显示版本信息 |

## 配置文件

### 生成配置文件

```bash
com2tcp --generate-config config.toml
```

### 配置文件格式

```toml
[serial]
port = "COM1"
baud_rate = 9600
data_bits = 8
stop_bits = 1
parity = "None"
timeout = 1000

[tcp]
host = "0.0.0.0"
port = 8080
max_clients = 10
client_timeout = 300000
buffer_size = 1024

[logging]
level = "info"
file = "com2tcp.log"

[limits]
max_message_size = 1048576
max_queue_size = 1000
```

### 使用配置文件

```bash
com2tcp --config config.toml
```

## 使用场景

### 1. 串口设备远程访问

```bash
# 在设备所在机器上启动
com2tcp --port COM1 --baud 9600 --tcp-host 0.0.0.0 --tcp-port 8080

# 从远程机器连接
telnet device_ip 8080
```

### 2. 多用户协作调试

```bash
# 启动服务器
com2tcp --port COM1 --baud 115200 --tcp-port 8080 --max-clients 5

# 多个用户同时连接
# 用户1: telnet server_ip 8080
# 用户2: nc server_ip 8080
# 用户3: 自定义客户端程序
```

### 3. 数据记录和分析

```bash
# 启动服务器并记录日志
com2tcp --port COM1 --baud 9600 --tcp-port 8080 \
         --log-file data.log --log-level debug

# 连接并保存数据
nc localhost 8080 | tee received_data.txt
```

### 4. 工业设备监控

```bash
# 高可靠性配置
com2tcp --port /dev/ttyUSB0 --baud 38400 \
         --tcp-port 8080 --max-clients 3 \
         --client-timeout 1800 --buffer-size 4096 \
         --log-file monitor.log
```

## 客户端连接

### 1. Telnet 客户端

```bash
telnet localhost 8080
```

### 2. Netcat 客户端

```bash
# Linux/macOS
nc localhost 8080

# Windows
nc.exe localhost 8080
```

### 3. Python 客户端示例

```python
import socket
import threading

def receive_data(sock):
    while True:
        try:
            data = sock.recv(1024)
            if not data:
                break
            print(f"Received: {data.decode('utf-8', errors='ignore')}")
        except:
            break

client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('localhost', 8080))

# 启动接收线程
receive_thread = threading.Thread(target=receive_data, args=(client,))
receive_thread.daemon = True
receive_thread.start()

# 发送数据
try:
    while True:
        message = input("Enter message: ")
        if message.lower() == 'quit':
            break
        client.send(message.encode('utf-8'))
finally:
    client.close()
```

### 4. C# 客户端示例

```csharp
using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        using var client = new TcpClient();
        await client.ConnectAsync("localhost", 8080);
        
        using var stream = client.GetStream();
        
        // 接收数据任务
        var receiveTask = Task.Run(async () =>
        {
            var buffer = new byte[1024];
            while (client.Connected)
            {
                try
                {
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead > 0)
                    {
                        string message = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        Console.WriteLine($"Received: {message}");
                    }
                }
                catch { break; }
            }
        });
        
        // 发送数据
        while (client.Connected)
        {
            Console.Write("Enter message: ");
            string input = Console.ReadLine();
            if (input?.ToLower() == "quit") break;
                
            byte[] data = Encoding.UTF8.GetBytes(input + "\r\n");
            await stream.WriteAsync(data, 0, data.Length);
        }
    }
}
```

## 监控和日志

### 日志级别

- **off**：关闭日志
- **error**：只记录错误
- **warn**：记录警告和错误
- **info**：记录信息、警告和错误（默认）
- **debug**：记录调试信息
- **trace**：记录所有信息

### 日志配置

```bash
# 输出到文件
com2tcp --port COM1 --log-file com2tcp.log --log-level debug

# 详细输出到控制台
com2tcp --port COM1 --verbose

# 只记录错误
com2tcp --port COM1 --log-level error
```

### 监控指标

程序会定期输出统计信息：

```
Forwarder Statistics:
  Uptime: 1h 23m 45s
  Active clients: 3
  Serial -> TCP: 1024000 bytes (5000 messages)
  TCP -> Serial: 512000 bytes (2500 messages)
  Errors: 0
  Last activity: 2s ago
```

## 性能优化

### 1. 高吞吐量配置

```bash
com2tcp --port COM1 --baud 115200 \
         --buffer-size 8192 --max-queue-size 5000 \
         --max-clients 20
```

### 2. 低延迟配置

```bash
com2tcp --port COM1 --baud 115200 \
         --buffer-size 512 --max-queue-size 100 \
         --timeout 100
```

### 3. 稳定性优先配置

```bash
com2tcp --port COM1 --baud 9600 \
         --client-timeout 1800 --max-clients 5 \
         --log-level warn
```

### 4. 资源限制

```bash
com2tcp --port COM1 \
         --max-message-size 65536 \
         --max-queue-size 1000 \
         --max-clients 10
```

## 故障排除

### 常见问题

1. **串口无法打开**
   - 检查串口是否存在：`com2tcp --list-ports`
   - 确认权限和占用情况
   - 参考 [故障排除指南](troubleshooting.md)

2. **TCP 端口绑定失败**
   - 检查端口占用：`netstat -an | grep 8080`
   - 更换端口或终止占用进程

3. **客户端连接问题**
   - 检查防火墙设置
   - 确认网络连通性
   - 测试本地连接

4. **数据传输问题**
   - 确认串口参数正确
   - 检查硬件连接
   - 启用调试日志

### 获取帮助

- 查看详细的[故障排除指南](troubleshooting.md)
- 查看 [API 参考文档](api_reference.md)
- 提交 GitHub Issue
- 查看在线文档

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
