use crate::config::TcpConfig;
use crate::error::{Tcp<PERSON><PERSON><PERSON>, Tcp<PERSON><PERSON><PERSON>};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::Instant;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{mpsc, Mutex, RwLock};
use tokio::time::{timeout, Duration};

/// Unique identifier for TCP clients
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub struct ClientId(u64);

impl ClientId {
    fn new() -> Self {
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        Self(COUNTER.fetch_add(1, Ordering::Relaxed))
    }
    
    pub fn as_u64(&self) -> u64 {
        self.0
    }
}

impl std::fmt::Display for ClientId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Client#{}", self.0)
    }
}

/// Information about a connected TCP client
#[derive(Debug)]
pub struct ClientConnection {
    pub id: ClientId,
    pub addr: SocketAddr,
    pub connected_at: Instant,
    pub last_activity: Instant,
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

impl ClientConnection {
    fn new(id: ClientId, addr: SocketAddr) -> Self {
        let now = Instant::now();
        Self {
            id,
            addr,
            connected_at: now,
            last_activity: now,
            bytes_sent: 0,
            bytes_received: 0,
        }
    }
    
    fn update_activity(&mut self) {
        self.last_activity = Instant::now();
    }
    
    fn add_bytes_sent(&mut self, bytes: u64) {
        self.bytes_sent += bytes;
        self.update_activity();
    }
    
    fn add_bytes_received(&mut self, bytes: u64) {
        self.bytes_received += bytes;
        self.update_activity();
    }
}

/// TCP server that manages multiple client connections
pub struct TcpServer {
    pub config: TcpConfig,
    listener: Option<TcpListener>,
    clients: Arc<RwLock<HashMap<ClientId, Arc<Mutex<ClientConnection>>>>>,
    event_tx: mpsc::Sender<TcpEvent>,
    command_rx: mpsc::Receiver<TcpCommand>,
    is_running: bool,
}

/// Handle for communicating with the TCP server
pub struct TcpHandle {
    command_tx: mpsc::Sender<TcpCommand>,
    event_rx: mpsc::Receiver<TcpEvent>,
}

/// Events emitted by the TCP server
#[derive(Debug, Clone)]
pub enum TcpEvent {
    /// Server started listening
    ServerStarted {
        bind_addr: SocketAddr,
    },
    
    /// Server stopped
    ServerStopped,
    
    /// New client connected
    ClientConnected {
        client_id: ClientId,
        addr: SocketAddr,
    },
    
    /// Client disconnected
    ClientDisconnected {
        client_id: ClientId,
        addr: SocketAddr,
        reason: String,
    },
    
    /// Data received from a client
    DataReceived {
        client_id: ClientId,
        data: Vec<u8>,
        timestamp: Instant,
    },
    
    /// Error occurred
    Error {
        error: TcpError,
        timestamp: Instant,
    },
    
    /// Server status update
    StatusUpdate {
        client_count: usize,
        total_bytes_sent: u64,
        total_bytes_received: u64,
    },
}

/// Commands that can be sent to the TCP server
#[derive(Debug)]
pub enum TcpCommand {
    /// Start the TCP server
    Start,
    
    /// Stop the TCP server
    Stop,
    
    /// Send data to a specific client
    SendToClient {
        client_id: ClientId,
        data: Vec<u8>,
    },
    
    /// Send data to all connected clients
    SendToAll {
        data: Vec<u8>,
    },
    
    /// Disconnect a specific client
    DisconnectClient {
        client_id: ClientId,
    },
    
    /// Get current server status
    GetStatus,
    
    /// Shutdown the TCP server
    Shutdown,
}

impl TcpServer {
    /// Create a new TCP server and its handle
    pub fn new(config: TcpConfig) -> (Self, TcpHandle) {
        let (event_tx, event_rx) = mpsc::channel(100);
        let (command_tx, command_rx) = mpsc::channel(100);
        
        let server = Self {
            config,
            listener: None,
            clients: Arc::new(RwLock::new(HashMap::new())),
            event_tx,
            command_rx,
            is_running: false,
        };
        
        let handle = TcpHandle {
            command_tx,
            event_rx,
        };
        
        (server, handle)
    }
    
    /// Run the TCP server main loop
    pub async fn run(&mut self) -> TcpResult<()> {
        info!("Starting TCP server on {}:{}", self.config.host, self.config.port);
        self.is_running = true;
        
        // Start the server
        if let Err(e) = self.start_server().await {
            error!("Failed to start TCP server: {}", e);
            self.send_event(TcpEvent::Error {
                error: e.clone(),
                timestamp: Instant::now(),
            }).await;
            return Err(e);
        }
        
        let mut status_interval = tokio::time::interval(Duration::from_secs(30));
        
        while self.is_running {
            tokio::select! {
                // Handle incoming commands
                command = self.command_rx.recv() => {
                    match command {
                        Some(cmd) => {
                            if let Err(e) = self.handle_command(cmd).await {
                                error!("Error handling TCP command: {}", e);
                                self.send_event(TcpEvent::Error {
                                    error: e,
                                    timestamp: Instant::now(),
                                }).await;
                            }
                        }
                        None => {
                            info!("TCP command channel closed, shutting down");
                            break;
                        }
                    }
                }
                
                // Accept new client connections
                result = self.accept_connection() => {
                    if let Err(e) = result {
                        error!("Error accepting connection: {}", e);
                        self.send_event(TcpEvent::Error {
                            error: e,
                            timestamp: Instant::now(),
                        }).await;
                    }
                }
                
                // Send periodic status updates
                _ = status_interval.tick() => {
                    self.send_status_update().await;
                }
            }
        }
        
        // Cleanup on shutdown
        self.stop_server().await?;
        info!("TCP server stopped");
        Ok(())
    }
    
    /// Start the TCP server listener
    async fn start_server(&mut self) -> TcpResult<()> {
        let bind_addr = format!("{}:{}", self.config.host, self.config.port);
        
        let listener = TcpListener::bind(&bind_addr).await
            .map_err(|e| TcpError::bind_failed(&self.config.host, self.config.port, e))?;
        
        let local_addr = listener.local_addr()
            .map_err(|e| TcpError::bind_failed(&self.config.host, self.config.port, e))?;
        
        info!("TCP server listening on {}", local_addr);
        
        self.listener = Some(listener);
        
        self.send_event(TcpEvent::ServerStarted {
            bind_addr: local_addr,
        }).await;
        
        Ok(())
    }
    
    /// Stop the TCP server
    async fn stop_server(&mut self) -> TcpResult<()> {
        // Close the listener
        self.listener = None;
        
        // Disconnect all clients
        let client_ids: Vec<ClientId> = {
            let clients = self.clients.read().await;
            clients.keys().copied().collect()
        };
        
        for client_id in client_ids {
            self.disconnect_client_internal(client_id, "Server shutdown").await;
        }
        
        self.send_event(TcpEvent::ServerStopped).await;

        Ok(())
    }

    /// Accept a new client connection
    async fn accept_connection(&mut self) -> TcpResult<()> {
        if let Some(listener) = &self.listener {
            match listener.accept().await {
                Ok((stream, addr)) => {
                    // Check if we've reached the maximum number of clients
                    let client_count = {
                        let clients = self.clients.read().await;
                        clients.len()
                    };

                    if client_count >= self.config.max_clients {
                        warn!("Maximum clients ({}) reached, rejecting connection from {}",
                              self.config.max_clients, addr);
                        return Err(TcpError::max_clients_exceeded(self.config.max_clients));
                    }

                    let client_id = ClientId::new();
                    let client_info = Arc::new(Mutex::new(ClientConnection::new(client_id, addr)));

                    // Add client to the clients map
                    {
                        let mut clients = self.clients.write().await;
                        clients.insert(client_id, client_info.clone());
                    }

                    info!("Client {} connected from {}", client_id, addr);

                    self.send_event(TcpEvent::ClientConnected {
                        client_id,
                        addr,
                    }).await;

                    // Spawn a task to handle this client
                    let clients_map = Arc::clone(&self.clients);
                    let event_tx = self.event_tx.clone();
                    let client_timeout = self.config.client_timeout;
                    let buffer_size = self.config.buffer_size;

                    tokio::spawn(async move {
                        if let Err(e) = Self::handle_client(
                            stream,
                            client_id,
                            client_info,
                            clients_map,
                            event_tx,
                            client_timeout,
                            buffer_size,
                        ).await {
                            error!("Error handling client {}: {}", client_id, e);
                        }
                    });
                }
                Err(e) => {
                    return Err(TcpError::ConnectionFailed { source: e });
                }
            }
        }

        Ok(())
    }

    /// Handle a client connection
    async fn handle_client(
        mut stream: TcpStream,
        client_id: ClientId,
        client_info: Arc<Mutex<ClientConnection>>,
        clients_map: Arc<RwLock<HashMap<ClientId, Arc<Mutex<ClientConnection>>>>>,
        event_tx: mpsc::Sender<TcpEvent>,
        client_timeout: Duration,
        buffer_size: usize,
    ) -> TcpResult<()> {
        let mut buffer = vec![0u8; buffer_size];
        let addr = stream.peer_addr().map_err(|e| TcpError::ConnectionFailed { source: e })?;

        loop {
            // Read data from the client with timeout
            match timeout(client_timeout, stream.read(&mut buffer)).await {
                Ok(Ok(0)) => {
                    // Client disconnected
                    info!("Client {} disconnected", client_id);
                    break;
                }
                Ok(Ok(bytes_read)) => {
                    // Data received
                    let data = buffer[..bytes_read].to_vec();

                    // Update client statistics
                    {
                        let mut client = client_info.lock().await;
                        client.add_bytes_received(bytes_read as u64);
                    }

                    debug!("Received {} bytes from client {}", bytes_read, client_id);

                    // Send event
                    if let Err(e) = event_tx.send(TcpEvent::DataReceived {
                        client_id,
                        data,
                        timestamp: Instant::now(),
                    }).await {
                        error!("Failed to send data received event: {}", e);
                        break;
                    }
                }
                Ok(Err(e)) => {
                    // Read error
                    error!("Error reading from client {}: {}", client_id, e);
                    break;
                }
                Err(_) => {
                    // Timeout
                    warn!("Client {} timed out", client_id);
                    if let Err(e) = event_tx.send(TcpEvent::Error {
                        error: TcpError::client_timeout(client_id.as_u64()),
                        timestamp: Instant::now(),
                    }).await {
                        error!("Failed to send timeout event: {}", e);
                    }
                    break;
                }
            }
        }

        // Remove client from the clients map
        {
            let mut clients = clients_map.write().await;
            clients.remove(&client_id);
        }

        // Send disconnection event
        if let Err(e) = event_tx.send(TcpEvent::ClientDisconnected {
            client_id,
            addr,
            reason: "Connection closed".to_string(),
        }).await {
            error!("Failed to send client disconnected event: {}", e);
        }

        Ok(())
    }

    /// Handle incoming commands
    async fn handle_command(&mut self, command: TcpCommand) -> TcpResult<()> {
        debug!("Handling TCP command: {:?}", command);

        match command {
            TcpCommand::Start => {
                if self.listener.is_none() {
                    self.start_server().await?;
                }
            }

            TcpCommand::Stop => {
                self.stop_server().await?;
            }

            TcpCommand::SendToClient { client_id, data } => {
                self.send_to_client(client_id, &data).await?;
            }

            TcpCommand::SendToAll { data } => {
                self.send_to_all(&data).await?;
            }

            TcpCommand::DisconnectClient { client_id } => {
                self.disconnect_client_internal(client_id, "Manual disconnect").await;
            }

            TcpCommand::GetStatus => {
                self.send_status_update().await;
            }

            TcpCommand::Shutdown => {
                info!("Received shutdown command");
                self.is_running = false;
            }
        }

        Ok(())
    }

    /// Send data to a specific client
    async fn send_to_client(&self, client_id: ClientId, data: &[u8]) -> TcpResult<()> {
        let client_info = {
            let clients = self.clients.read().await;
            clients.get(&client_id).cloned()
        };

        if let Some(client_info) = client_info {
            // TODO: Implement actual data sending to client
            // This would require storing the TcpStream in ClientConnection
            // For now, we'll just update statistics
            {
                let mut client = client_info.lock().await;
                client.add_bytes_sent(data.len() as u64);
            }

            debug!("Sent {} bytes to client {}", data.len(), client_id);
        } else {
            return Err(TcpError::ClientDisconnected { client_id: client_id.as_u64() });
        }

        Ok(())
    }

    /// Send data to all connected clients
    async fn send_to_all(&self, data: &[u8]) -> TcpResult<()> {
        let client_ids: Vec<ClientId> = {
            let clients = self.clients.read().await;
            clients.keys().copied().collect()
        };

        for client_id in client_ids {
            if let Err(e) = self.send_to_client(client_id, data).await {
                warn!("Failed to send data to client {}: {}", client_id, e);
            }
        }

        Ok(())
    }

    /// Disconnect a client
    async fn disconnect_client_internal(&self, client_id: ClientId, reason: &str) {
        let client_info = {
            let mut clients = self.clients.write().await;
            clients.remove(&client_id)
        };

        if let Some(client_info) = client_info {
            let addr = {
                let client = client_info.lock().await;
                client.addr
            };

            info!("Disconnecting client {} ({}): {}", client_id, addr, reason);

            self.send_event(TcpEvent::ClientDisconnected {
                client_id,
                addr,
                reason: reason.to_string(),
            }).await;
        }
    }

    /// Send a status update event
    async fn send_status_update(&self) {
        let (client_count, total_bytes_sent, total_bytes_received) = {
            let clients = self.clients.read().await;
            let client_count = clients.len();

            let mut total_sent = 0u64;
            let mut total_received = 0u64;

            for client_info in clients.values() {
                let client = client_info.lock().await;
                total_sent += client.bytes_sent;
                total_received += client.bytes_received;
            }

            (client_count, total_sent, total_received)
        };

        self.send_event(TcpEvent::StatusUpdate {
            client_count,
            total_bytes_sent,
            total_bytes_received,
        }).await;
    }

    /// Send an event to the event channel
    async fn send_event(&self, event: TcpEvent) {
        if let Err(e) = self.event_tx.send(event).await {
            error!("Failed to send TCP event: {}", e);
        }
    }
}

impl TcpHandle {
    /// Send a command to the TCP server
    pub async fn send_command(&self, command: TcpCommand) -> TcpResult<()> {
        self.command_tx.send(command).await
            .map_err(|_| TcpError::ConnectionFailed {
                source: std::io::Error::new(
                    std::io::ErrorKind::BrokenPipe,
                    "TCP server command channel closed"
                )
            })
    }

    /// Receive an event from the TCP server
    pub async fn recv_event(&mut self) -> Option<TcpEvent> {
        self.event_rx.recv().await
    }

    /// Try to receive an event without blocking
    pub fn try_recv_event(&mut self) -> Result<TcpEvent, mpsc::error::TryRecvError> {
        self.event_rx.try_recv()
    }

    /// Start the TCP server
    pub async fn start(&self) -> TcpResult<()> {
        self.send_command(TcpCommand::Start).await
    }

    /// Stop the TCP server
    pub async fn stop(&self) -> TcpResult<()> {
        self.send_command(TcpCommand::Stop).await
    }

    /// Send data to a specific client
    pub async fn send_to_client(&self, client_id: ClientId, data: Vec<u8>) -> TcpResult<()> {
        self.send_command(TcpCommand::SendToClient { client_id, data }).await
    }

    /// Send data to all connected clients
    pub async fn send_to_all(&self, data: Vec<u8>) -> TcpResult<()> {
        self.send_command(TcpCommand::SendToAll { data }).await
    }

    /// Disconnect a specific client
    pub async fn disconnect_client(&self, client_id: ClientId) -> TcpResult<()> {
        self.send_command(TcpCommand::DisconnectClient { client_id }).await
    }

    /// Get current server status
    pub async fn get_status(&self) -> TcpResult<()> {
        self.send_command(TcpCommand::GetStatus).await
    }

    /// Shutdown the TCP server
    pub async fn shutdown(&self) -> TcpResult<()> {
        self.send_command(TcpCommand::Shutdown).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> TcpConfig {
        TcpConfig {
            host: "127.0.0.1".to_string(),
            port: 0, // Use port 0 to let the OS choose
            max_clients: 5,
            client_timeout: Duration::from_secs(30),
            buffer_size: 1024,
        }
    }

    #[test]
    fn test_client_id_generation() {
        let id1 = ClientId::new();
        let id2 = ClientId::new();

        assert_ne!(id1, id2);
        assert!(id1.as_u64() > 0);
        assert!(id2.as_u64() > 0);
    }

    #[test]
    fn test_client_connection() {
        let id = ClientId::new();
        let addr = "127.0.0.1:8080".parse().unwrap();
        let mut conn = ClientConnection::new(id, addr);

        assert_eq!(conn.id, id);
        assert_eq!(conn.addr, addr);
        assert_eq!(conn.bytes_sent, 0);
        assert_eq!(conn.bytes_received, 0);

        conn.add_bytes_sent(100);
        conn.add_bytes_received(200);

        assert_eq!(conn.bytes_sent, 100);
        assert_eq!(conn.bytes_received, 200);
    }

    #[tokio::test]
    async fn test_tcp_server_creation() {
        let config = create_test_config();
        let (server, handle) = TcpServer::new(config.clone());

        assert_eq!(server.config.host, config.host);
        assert_eq!(server.config.port, config.port);
        assert!(!server.is_running);
        assert!(server.listener.is_none());
    }

    #[tokio::test]
    async fn test_tcp_handle_commands() {
        let config = create_test_config();
        let (_server, handle) = TcpServer::new(config);

        // Test sending commands (they will fail because no server is running)
        let result = handle.start().await;
        assert!(result.is_err());
    }
}
