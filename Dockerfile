# COM2TCP Dockerfile
# 多阶段构建，优化镜像大小

# 构建阶段
FROM rust:1.75-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libudev-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 Cargo 文件
COPY Cargo.toml Cargo.lock ./

# 创建一个虚拟的 main.rs 来缓存依赖
RUN mkdir src && echo "fn main() {}" > src/main.rs

# 构建依赖（这一层会被缓存）
RUN cargo build --release && rm -rf src

# 复制源代码
COPY src ./src

# 构建应用程序
RUN cargo build --release

# 运行阶段
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libudev1 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -r -s /bin/false com2tcp

# 创建应用目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/target/release/com2tcp /usr/local/bin/com2tcp

# 复制配置文件模板
COPY docs/examples/config.toml /app/config.toml.example

# 创建日志目录
RUN mkdir -p /app/logs && chown com2tcp:com2tcp /app/logs

# 创建配置目录
RUN mkdir -p /app/config && chown com2tcp:com2tcp /app/config

# 设置权限
RUN chmod +x /usr/local/bin/com2tcp

# 暴露默认TCP端口
EXPOSE 8080

# 创建挂载点
VOLUME ["/app/logs", "/app/config"]

# 切换到非root用户
USER com2tcp

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD netstat -an | grep :8080 || exit 1

# 设置默认命令
ENTRYPOINT ["/usr/local/bin/com2tcp"]
CMD ["--help"]

# 元数据标签
LABEL maintainer="COM2TCP Contributors" \
      description="High-performance serial port to TCP forwarder" \
      version="0.1.0" \
      org.opencontainers.image.title="COM2TCP" \
      org.opencontainers.image.description="A bidirectional serial port to TCP forwarder supporting multiple concurrent clients" \
      org.opencontainers.image.url="https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git" \
      org.opencontainers.image.source="https://g-eego8432.coding.net/p/kaiyuanxiangmu/d/com2tcp/git" \
      org.opencontainers.image.vendor="COM2TCP Contributors" \
      org.opencontainers.image.licenses="MIT"
