@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo         Rust Installation Helper
echo ========================================
echo.

:: 检查是否已安装Rust
where cargo >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Rust is already installed!
    echo.
    rustc --version
    cargo --version
    echo.
    echo If you want to update Rust, run: rustup update
    echo.
    pause
    exit /b 0
)

echo [INFO] Rust is not installed. Starting installation process...
echo.

:: 检查网络连接
echo [1/3] Checking internet connection...
ping -n 1 www.rust-lang.org >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Cannot connect to rust-lang.org
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)
echo [OK] Internet connection available
echo.

:: 下载rustup-init.exe
echo [2/3] Downloading Rust installer...
echo.
echo This will download rustup-init.exe from https://rustup.rs/
echo.
echo Please choose an option:
echo 1. Open browser to download manually
echo 2. Try automatic download (requires PowerShell)
echo 3. Cancel
echo.
set /p download_choice="Enter your choice (1-3): "

if "%download_choice%"=="1" goto :manual_download
if "%download_choice%"=="2" goto :auto_download
if "%download_choice%"=="3" goto :cancel
echo Invalid choice. Please try again.
goto :download_choice

:manual_download
echo.
echo [INFO] Opening browser to https://rustup.rs/
echo.
echo Please:
echo 1. Download rustup-init.exe from the website
echo 2. Run the downloaded file
echo 3. Follow the installation instructions
echo 4. Restart your command prompt
echo 5. Run check_rust.bat to verify installation
echo.
start https://rustup.rs/
pause
exit /b 0

:auto_download
echo.
echo [INFO] Attempting automatic download...
echo.

:: 使用PowerShell下载
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://win.rustup.rs/x86_64' -OutFile 'rustup-init.exe'}"

if exist "rustup-init.exe" (
    echo [OK] Download completed: rustup-init.exe
    goto :install
) else (
    echo [ERROR] Download failed. Please try manual download.
    goto :manual_download
)

:install
echo.
echo [3/3] Running Rust installer...
echo.
echo The installer will now start. Please:
echo 1. Choose option 1 (Proceed with installation - default)
echo 2. Wait for installation to complete
echo 3. Press any key when installation is finished
echo.
echo Starting installer...
rustup-init.exe

echo.
echo [INFO] Installation completed!
echo.
echo Please:
echo 1. Close this command prompt window
echo 2. Open a new command prompt window
echo 3. Run check_rust.bat to verify installation
echo.

:: 清理下载的文件
if exist "rustup-init.exe" (
    echo Cleaning up downloaded file...
    del rustup-init.exe
)

pause
exit /b 0

:cancel
echo.
echo [INFO] Installation cancelled.
echo.
echo To install Rust later:
echo 1. Visit https://rustup.rs/
echo 2. Download and run rustup-init.exe
echo 3. Follow the installation instructions
echo.
pause
exit /b 0
