@echo off

echo.
echo Rust Installation Helper
echo ========================
echo.

where cargo >nul 2>&1
if %errorlevel% equ 0 (
    echo Rust is already installed!
    echo.
    rustc --version
    cargo --version
    echo.
    echo To update Rust, run: rustup update
    echo.
    pause
    exit /b 0
)

echo Rust is not installed.
echo.
echo To install Rust:
echo 1. This script will open https://rustup.rs/ in your browser
echo 2. Download rustup-init.exe from the website
echo 3. Run the downloaded file
echo 4. Choose option 1 (default installation)
echo 5. Restart your command prompt after installation
echo 6. Run simple_check.bat to verify
echo.
echo Press any key to open the Rust website...
pause >nul

start https://rustup.rs/

echo.
echo Browser opened. Please download and run rustup-init.exe
echo.
echo After installation:
echo 1. Close this command prompt
echo 2. Open a new command prompt
echo 3. Run simple_check.bat
echo.
pause
