use crate::cli::Args;
use crate::error::{Com2TcpError, Result};
use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Main application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub serial: SerialConfig,
    pub tcp: TcpConfig,
    pub logging: LogConfig,
    pub limits: LimitsConfig,
}

/// Serial port configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SerialConfig {
    pub port: String,
    pub baud_rate: u32,
    pub data_bits: serialport::DataBits,
    pub stop_bits: serialport::StopBits,
    pub parity: serialport::Parity,
    #[serde(with = "duration_serde")]
    pub timeout: Duration,
}

/// TCP server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TcpConfig {
    pub host: String,
    pub port: u16,
    pub max_clients: usize,
    #[serde(with = "duration_serde")]
    pub client_timeout: Duration,
    pub buffer_size: usize,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    #[serde(with = "log_level_serde")]
    pub level: log::LevelFilter,
    pub file: Option<String>,
}

/// Resource limits configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LimitsConfig {
    pub max_message_size: usize,
    pub max_queue_size: usize,
}

impl Config {
    /// Create configuration from command line arguments
    pub fn from_args(args: Args) -> Result<Self> {
        let serial = SerialConfig {
            port: args.port,
            baud_rate: args.baud,
            data_bits: parse_data_bits(args.data_bits)?,
            stop_bits: parse_stop_bits(args.stop_bits)?,
            parity: parse_parity(&args.parity)?,
            timeout: Duration::from_millis(args.timeout),
        };
        
        let tcp = TcpConfig {
            host: args.tcp_host,
            port: args.tcp_port,
            max_clients: args.max_clients,
            client_timeout: Duration::from_secs(args.client_timeout),
            buffer_size: args.buffer_size,
        };
        
        let logging = LogConfig {
            level: parse_log_level(&args.log_level)?,
            file: args.log_file,
        };
        
        let limits = LimitsConfig {
            max_message_size: args.max_message_size,
            max_queue_size: args.max_queue_size,
        };
        
        Ok(Self {
            serial,
            tcp,
            logging,
            limits,
        })
    }
    
    /// Load configuration from TOML file
    pub fn from_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| Com2TcpError::config_error(&format!("Failed to read config file '{}': {}", path, e)))?;
        
        toml::from_str(&content)
            .map_err(|e| Com2TcpError::config_error(&format!("Failed to parse config file '{}': {}", path, e)))
    }
    
    /// Save configuration to TOML file
    pub fn save_to_file(&self, path: &str) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| Com2TcpError::config_error(&format!("Failed to serialize config: {}", e)))?;
        
        std::fs::write(path, content)
            .map_err(|e| Com2TcpError::config_error(&format!("Failed to write config file '{}': {}", path, e)))?;
        
        Ok(())
    }
    
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate serial configuration
        if self.serial.port.is_empty() {
            return Err(Com2TcpError::config_error("Serial port cannot be empty"));
        }
        
        if self.serial.baud_rate == 0 {
            return Err(Com2TcpError::config_error("Baud rate must be greater than 0"));
        }
        
        // Validate TCP configuration
        if self.tcp.port == 0 {
            return Err(Com2TcpError::config_error("TCP port must be greater than 0"));
        }
        
        if self.tcp.max_clients == 0 {
            return Err(Com2TcpError::config_error("Max clients must be greater than 0"));
        }
        
        if self.tcp.buffer_size == 0 {
            return Err(Com2TcpError::config_error("Buffer size must be greater than 0"));
        }
        
        // Validate limits
        if self.limits.max_message_size == 0 {
            return Err(Com2TcpError::config_error("Max message size must be greater than 0"));
        }
        
        if self.limits.max_queue_size == 0 {
            return Err(Com2TcpError::config_error("Max queue size must be greater than 0"));
        }
        
        Ok(())
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            serial: SerialConfig::default(),
            tcp: TcpConfig::default(),
            logging: LogConfig::default(),
            limits: LimitsConfig::default(),
        }
    }
}

impl Default for SerialConfig {
    fn default() -> Self {
        Self {
            port: if cfg!(windows) { "COM1".to_string() } else { "/dev/ttyUSB0".to_string() },
            baud_rate: 9600,
            data_bits: serialport::DataBits::Eight,
            stop_bits: serialport::StopBits::One,
            parity: serialport::Parity::None,
            timeout: Duration::from_millis(1000),
        }
    }
}

impl Default for TcpConfig {
    fn default() -> Self {
        Self {
            host: "0.0.0.0".to_string(),
            port: 8080,
            max_clients: 10,
            client_timeout: Duration::from_secs(300),
            buffer_size: 1024,
        }
    }
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: log::LevelFilter::Info,
            file: None,
        }
    }
}

impl Default for LimitsConfig {
    fn default() -> Self {
        Self {
            max_message_size: 1024 * 1024, // 1MB
            max_queue_size: 1000,
        }
    }
}

// Helper functions for parsing command line arguments

fn parse_data_bits(bits: u8) -> Result<serialport::DataBits> {
    match bits {
        5 => Ok(serialport::DataBits::Five),
        6 => Ok(serialport::DataBits::Six),
        7 => Ok(serialport::DataBits::Seven),
        8 => Ok(serialport::DataBits::Eight),
        _ => Err(Com2TcpError::config_error(&format!("Invalid data bits: {}", bits))),
    }
}

fn parse_stop_bits(bits: u8) -> Result<serialport::StopBits> {
    match bits {
        1 => Ok(serialport::StopBits::One),
        2 => Ok(serialport::StopBits::Two),
        _ => Err(Com2TcpError::config_error(&format!("Invalid stop bits: {}", bits))),
    }
}

fn parse_parity(parity: &str) -> Result<serialport::Parity> {
    match parity.to_lowercase().as_str() {
        "none" | "n" => Ok(serialport::Parity::None),
        "odd" | "o" => Ok(serialport::Parity::Odd),
        "even" | "e" => Ok(serialport::Parity::Even),
        _ => Err(Com2TcpError::config_error(&format!("Invalid parity: {}", parity))),
    }
}

fn parse_log_level(level: &str) -> Result<log::LevelFilter> {
    match level.to_lowercase().as_str() {
        "off" => Ok(log::LevelFilter::Off),
        "error" => Ok(log::LevelFilter::Error),
        "warn" => Ok(log::LevelFilter::Warn),
        "info" => Ok(log::LevelFilter::Info),
        "debug" => Ok(log::LevelFilter::Debug),
        "trace" => Ok(log::LevelFilter::Trace),
        _ => Err(Com2TcpError::config_error(&format!("Invalid log level: {}", level))),
    }
}

// Serde helpers for Duration and log::LevelFilter

mod duration_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use std::time::Duration;
    
    pub fn serialize<S>(duration: &Duration, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        duration.as_millis().serialize(serializer)
    }
    
    pub fn deserialize<'de, D>(deserializer: D) -> Result<Duration, D::Error>
    where
        D: Deserializer<'de>,
    {
        let millis = u64::deserialize(deserializer)?;
        Ok(Duration::from_millis(millis))
    }
}

mod log_level_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use log::LevelFilter;
    
    pub fn serialize<S>(level: &LevelFilter, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let level_str = match level {
            LevelFilter::Off => "off",
            LevelFilter::Error => "error",
            LevelFilter::Warn => "warn",
            LevelFilter::Info => "info",
            LevelFilter::Debug => "debug",
            LevelFilter::Trace => "trace",
        };
        level_str.serialize(serializer)
    }
    
    pub fn deserialize<'de, D>(deserializer: D) -> Result<LevelFilter, D::Error>
    where
        D: Deserializer<'de>,
    {
        let level_str = String::deserialize(deserializer)?;
        match level_str.to_lowercase().as_str() {
            "off" => Ok(LevelFilter::Off),
            "error" => Ok(LevelFilter::Error),
            "warn" => Ok(LevelFilter::Warn),
            "info" => Ok(LevelFilter::Info),
            "debug" => Ok(LevelFilter::Debug),
            "trace" => Ok(LevelFilter::Trace),
            _ => Err(serde::de::Error::custom(format!("Invalid log level: {}", level_str))),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_parse_data_bits() {
        assert!(matches!(parse_data_bits(8).unwrap(), serialport::DataBits::Eight));
        assert!(parse_data_bits(9).is_err());
    }
    
    #[test]
    fn test_parse_parity() {
        assert!(matches!(parse_parity("none").unwrap(), serialport::Parity::None));
        assert!(matches!(parse_parity("EVEN").unwrap(), serialport::Parity::Even));
        assert!(parse_parity("invalid").is_err());
    }
    
    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        assert!(config.validate().is_ok());
        
        config.serial.port = String::new();
        assert!(config.validate().is_err());
    }
}
