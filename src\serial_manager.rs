use crate::config::SerialConfig;
use crate::error::{SerialError, SerialResult};
use log::{debug, error, info, warn};
use serialport::{SerialPort, SerialPortBuilder};
use std::io::{Read, Write};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};
use tokio::time;

/// Unique identifier for serial events
pub type SerialEventId = u64;

/// Serial port manager handles all serial port operations
pub struct SerialManager {
    pub config: SerialConfig,
    port: Arc<Mutex<Option<Box<dyn SerialPort>>>>,
    event_tx: mpsc::Sender<SerialEvent>,
    command_rx: mpsc::Receiver<SerialCommand>,
    is_running: bool,
    reconnect_attempts: u32,
    last_activity: Instant,
    bytes_sent: u64,
    bytes_received: u64,
}

/// Handle for communicating with the serial manager
pub struct SerialHandle {
    command_tx: mpsc::Sender<SerialCommand>,
    event_rx: mpsc::Receiver<SerialEvent>,
}

/// Events emitted by the serial manager
#[derive(Debug, Clone)]
pub enum SerialEvent {
    /// Serial port successfully connected
    Connected {
        port_name: String,
        config: SerialConfig,
    },
    
    /// Serial port disconnected
    Disconnected {
        port_name: String,
        reason: String,
    },
    
    /// Data received from serial port
    DataReceived {
        data: Vec<u8>,
        timestamp: Instant,
    },
    
    /// Error occurred during serial operation
    Error {
        error: SerialError,
        timestamp: Instant,
    },
    
    /// Serial port status update
    StatusUpdate {
        connected: bool,
        bytes_sent: u64,
        bytes_received: u64,
        last_activity: Instant,
    },
}

/// Commands that can be sent to the serial manager
#[derive(Debug)]
pub enum SerialCommand {
    /// Connect to the serial port
    Connect,
    
    /// Disconnect from the serial port
    Disconnect,
    
    /// Send data to the serial port
    SendData { data: Vec<u8> },
    
    /// Request current status
    GetStatus,
    
    /// Shutdown the serial manager
    Shutdown,
}

impl SerialManager {
    /// Create a new serial manager and its handle
    pub fn new(config: SerialConfig) -> (Self, SerialHandle) {
        let (event_tx, event_rx) = mpsc::channel(100);
        let (command_tx, command_rx) = mpsc::channel(100);
        
        let manager = Self {
            config,
            port: Arc::new(Mutex::new(None)),
            event_tx,
            command_rx,
            is_running: false,
            reconnect_attempts: 0,
            last_activity: Instant::now(),
            bytes_sent: 0,
            bytes_received: 0,
        };
        
        let handle = SerialHandle {
            command_tx,
            event_rx,
        };
        
        (manager, handle)
    }
    
    /// Run the serial manager main loop
    pub async fn run(&mut self) -> SerialResult<()> {
        info!("Starting serial manager for port: {}", self.config.port);
        self.is_running = true;
        
        // Try initial connection
        if let Err(e) = self.connect().await {
            warn!("Initial connection failed: {}", e);
            self.send_event(SerialEvent::Error {
                error: e,
                timestamp: Instant::now(),
            }).await;
        }
        
        let mut read_interval = time::interval(Duration::from_millis(10));
        let mut status_interval = time::interval(Duration::from_secs(30));
        
        while self.is_running {
            tokio::select! {
                // Handle incoming commands
                command = self.command_rx.recv() => {
                    match command {
                        Some(cmd) => {
                            if let Err(e) = self.handle_command(cmd).await {
                                error!("Error handling command: {}", e);
                                self.send_event(SerialEvent::Error {
                                    error: e,
                                    timestamp: Instant::now(),
                                }).await;
                            }
                        }
                        None => {
                            info!("Command channel closed, shutting down");
                            break;
                        }
                    }
                }
                
                // Read data from serial port
                _ = read_interval.tick() => {
                    if let Err(e) = self.read_data().await {
                        if !matches!(e, SerialError::ReadTimeout { .. }) {
                            error!("Error reading data: {}", e);
                            self.send_event(SerialEvent::Error {
                                error: e,
                                timestamp: Instant::now(),
                            }).await;
                        }
                    }
                }
                
                // Send periodic status updates
                _ = status_interval.tick() => {
                    self.send_status_update().await;
                }
            }
        }
        
        // Cleanup on shutdown
        self.disconnect().await?;
        info!("Serial manager stopped");
        Ok(())
    }
    
    /// Connect to the serial port
    async fn connect(&mut self) -> SerialResult<()> {
        let mut port_guard = self.port.lock().await;
        
        if port_guard.is_some() {
            return Ok(()); // Already connected
        }
        
        info!("Connecting to serial port: {}", self.config.port);
        
        let port = serialport::new(&self.config.port, self.config.baud_rate)
            .data_bits(self.config.data_bits)
            .stop_bits(self.config.stop_bits)
            .parity(self.config.parity)
            .timeout(self.config.timeout)
            .open()
            .map_err(|e| SerialError::OpenFailed {
                port: self.config.port.clone(),
                source: e,
            })?;
        
        *port_guard = Some(port);
        self.reconnect_attempts = 0;
        self.last_activity = Instant::now();
        
        info!("Successfully connected to serial port: {}", self.config.port);
        
        self.send_event(SerialEvent::Connected {
            port_name: self.config.port.clone(),
            config: self.config.clone(),
        }).await;
        
        Ok(())
    }
    
    /// Disconnect from the serial port
    async fn disconnect(&mut self) -> SerialResult<()> {
        let mut port_guard = self.port.lock().await;

        if let Some(_) = port_guard.take() {
            info!("Disconnected from serial port: {}", self.config.port);

            self.send_event(SerialEvent::Disconnected {
                port_name: self.config.port.clone(),
                reason: "Manual disconnect".to_string(),
            }).await;
        }

        Ok(())
    }

    /// Handle incoming commands
    async fn handle_command(&mut self, command: SerialCommand) -> SerialResult<()> {
        debug!("Handling serial command: {:?}", command);

        match command {
            SerialCommand::Connect => {
                self.connect().await?;
            }

            SerialCommand::Disconnect => {
                self.disconnect().await?;
            }

            SerialCommand::SendData { data } => {
                self.write_data(&data).await?;
            }

            SerialCommand::GetStatus => {
                self.send_status_update().await;
            }

            SerialCommand::Shutdown => {
                info!("Received shutdown command");
                self.is_running = false;
            }
        }

        Ok(())
    }

    /// Read data from the serial port
    async fn read_data(&mut self) -> SerialResult<()> {
        let port_guard = self.port.lock().await;

        if let Some(port) = port_guard.as_ref() {
            let mut buffer = vec![0u8; 1024];

            // Clone the port for reading (we need to work around the async/sync boundary)
            let mut port_clone = port.try_clone()
                .map_err(|e| SerialError::OperationFailed(e))?;

            // Drop the guard before the blocking operation
            drop(port_guard);

            // Perform the blocking read operation in a separate thread
            let result = tokio::task::spawn_blocking(move || {
                match port_clone.read(&mut buffer) {
                    Ok(bytes_read) if bytes_read > 0 => {
                        buffer.truncate(bytes_read);
                        Ok(Some(buffer))
                    }
                    Ok(_) => Ok(None), // No data available
                    Err(e) if e.kind() == std::io::ErrorKind::TimedOut => {
                        Err(SerialError::ReadTimeout {
                            port: "unknown".to_string(), // We'll fix this
                        })
                    }
                    Err(e) => Err(SerialError::OperationFailed(
                        serialport::Error::from(e)
                    )),
                }
            }).await.map_err(|e| SerialError::OperationFailed(
                serialport::Error::Io(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Task join error: {}", e)
                ))
            ))?;

            match result {
                Ok(Some(data)) => {
                    self.bytes_received += data.len() as u64;
                    self.last_activity = Instant::now();

                    debug!("Received {} bytes from serial port", data.len());

                    self.send_event(SerialEvent::DataReceived {
                        data,
                        timestamp: Instant::now(),
                    }).await;
                }
                Ok(None) => {
                    // No data available, this is normal
                }
                Err(e) => {
                    return Err(e);
                }
            }
        }

        Ok(())
    }

    /// Write data to the serial port
    async fn write_data(&mut self, data: &[u8]) -> SerialResult<()> {
        let port_guard = self.port.lock().await;

        if let Some(port) = port_guard.as_ref() {
            let mut port_clone = port.try_clone()
                .map_err(|e| SerialError::OperationFailed(e))?;

            let data_to_write = data.to_vec();
            let port_name = self.config.port.clone();

            // Drop the guard before the blocking operation
            drop(port_guard);

            // Perform the blocking write operation in a separate thread
            let result = tokio::task::spawn_blocking(move || {
                port_clone.write_all(&data_to_write)
                    .and_then(|_| port_clone.flush())
                    .map_err(|e| SerialError::WriteFailed {
                        port: port_name,
                        source: serialport::Error::from(e),
                    })
            }).await.map_err(|e| SerialError::OperationFailed(
                serialport::Error::Io(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Task join error: {}", e)
                ))
            ))?;

            result?;

            self.bytes_sent += data.len() as u64;
            self.last_activity = Instant::now();

            debug!("Sent {} bytes to serial port", data.len());
        } else {
            return Err(SerialError::disconnected(&self.config.port));
        }

        Ok(())
    }

    /// Send a status update event
    async fn send_status_update(&self) {
        let connected = {
            let port_guard = self.port.lock().await;
            port_guard.is_some()
        };

        self.send_event(SerialEvent::StatusUpdate {
            connected,
            bytes_sent: self.bytes_sent,
            bytes_received: self.bytes_received,
            last_activity: self.last_activity,
        }).await;
    }

    /// Send an event to the event channel
    async fn send_event(&self, event: SerialEvent) {
        if let Err(e) = self.event_tx.send(event).await {
            error!("Failed to send serial event: {}", e);
        }
    }
}

impl SerialHandle {
    /// Send a command to the serial manager
    pub async fn send_command(&self, command: SerialCommand) -> SerialResult<()> {
        self.command_tx.send(command).await
            .map_err(|_| SerialError::OperationFailed(
                serialport::Error::Io(std::io::Error::new(
                    std::io::ErrorKind::BrokenPipe,
                    "Serial manager command channel closed"
                ))
            ))
    }

    /// Receive an event from the serial manager
    pub async fn recv_event(&mut self) -> Option<SerialEvent> {
        self.event_rx.recv().await
    }

    /// Try to receive an event without blocking
    pub fn try_recv_event(&mut self) -> Result<SerialEvent, mpsc::error::TryRecvError> {
        self.event_rx.try_recv()
    }

    /// Connect to the serial port
    pub async fn connect(&self) -> SerialResult<()> {
        self.send_command(SerialCommand::Connect).await
    }

    /// Disconnect from the serial port
    pub async fn disconnect(&self) -> SerialResult<()> {
        self.send_command(SerialCommand::Disconnect).await
    }

    /// Send data to the serial port
    pub async fn send_data(&self, data: Vec<u8>) -> SerialResult<()> {
        self.send_command(SerialCommand::SendData { data }).await
    }

    /// Request status update
    pub async fn get_status(&self) -> SerialResult<()> {
        self.send_command(SerialCommand::GetStatus).await
    }

    /// Shutdown the serial manager
    pub async fn shutdown(&self) -> SerialResult<()> {
        self.send_command(SerialCommand::Shutdown).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    fn create_test_config() -> SerialConfig {
        SerialConfig {
            port: "COM1".to_string(),
            baud_rate: 9600,
            data_bits: serialport::DataBits::Eight,
            stop_bits: serialport::StopBits::One,
            parity: serialport::Parity::None,
            timeout: Duration::from_millis(100),
        }
    }

    #[tokio::test]
    async fn test_serial_manager_creation() {
        let config = create_test_config();
        let (manager, handle) = SerialManager::new(config.clone());

        assert_eq!(manager.config.port, config.port);
        assert_eq!(manager.config.baud_rate, config.baud_rate);
        assert!(!manager.is_running);
        assert_eq!(manager.bytes_sent, 0);
        assert_eq!(manager.bytes_received, 0);
    }

    #[tokio::test]
    async fn test_serial_handle_commands() {
        let config = create_test_config();
        let (_manager, handle) = SerialManager::new(config);

        // Test sending commands (they will fail because no manager is running)
        let result = handle.connect().await;
        assert!(result.is_err());
    }

    #[test]
    fn test_serial_event_debug() {
        let event = SerialEvent::Connected {
            port_name: "COM1".to_string(),
            config: create_test_config(),
        };

        let debug_str = format!("{:?}", event);
        assert!(debug_str.contains("Connected"));
        assert!(debug_str.contains("COM1"));
    }

    #[test]
    fn test_serial_command_debug() {
        let command = SerialCommand::SendData {
            data: vec![1, 2, 3, 4],
        };

        let debug_str = format!("{:?}", command);
        assert!(debug_str.contains("SendData"));
    }
}
