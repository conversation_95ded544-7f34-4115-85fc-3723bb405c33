use anyhow::Result;
use log::{error, info};

mod cli;
mod config;
mod data_forwarder;
mod error;
mod serial_manager;
mod tcp_server;

use cli::Args;
use config::Config;
use data_forwarder::DataForwarder;
use serial_manager::SerialManager;
use tcp_server::TcpServer;

/// COM2TCP Application main structure
pub struct Application {
    config: Config,
    serial_manager: SerialManager,
    tcp_server: TcpServer,
    data_forwarder: DataForwarder,
}

impl Application {
    /// Create a new application instance
    pub async fn new(config: Config) -> Result<Self> {
        info!("Initializing COM2TCP application");
        
        // Create serial manager
        let (serial_manager, serial_handle) = SerialManager::new(config.serial.clone());
        
        // Create TCP server
        let (tcp_server, tcp_handle) = TcpServer::new(config.tcp.clone());
        
        // Create data forwarder
        let data_forwarder = DataForwarder::new(serial_handle, tcp_handle);
        
        Ok(Self {
            config,
            serial_manager,
            tcp_server,
            data_forwarder,
        })
    }
    
    /// Run the application
    pub async fn run(&mut self) -> Result<()> {
        info!("Starting COM2TCP application");
        info!("Serial port: {}", self.config.serial.port);
        info!("TCP server: {}:{}", self.config.tcp.host, self.config.tcp.port);
        
        // Start all components concurrently
        let mut serial_manager = self.serial_manager;
        let serial_task = tokio::spawn(async move {
            if let Err(e) = serial_manager.run().await {
                error!("Serial manager error: {}", e);
            }
        });

        let mut tcp_server = self.tcp_server;
        let tcp_task = tokio::spawn(async move {
            if let Err(e) = tcp_server.run().await {
                error!("TCP server error: {}", e);
            }
        });

        let mut data_forwarder = self.data_forwarder;
        let forwarder_task = tokio::spawn(async move {
            if let Err(e) = data_forwarder.run().await {
                error!("Data forwarder error: {}", e);
            }
        });
        
        // Wait for any task to complete (which usually means an error occurred)
        tokio::select! {
            _ = serial_task => {
                error!("Serial manager task completed unexpectedly");
            }
            _ = tcp_task => {
                error!("TCP server task completed unexpectedly");
            }
            _ = forwarder_task => {
                error!("Data forwarder task completed unexpectedly");
            }
            _ = tokio::signal::ctrl_c() => {
                info!("Received Ctrl+C, shutting down gracefully");
            }
        }
        
        self.shutdown().await
    }
    
    /// Shutdown the application gracefully
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down COM2TCP application");
        
        // TODO: Implement graceful shutdown for all components
        // This should:
        // 1. Stop accepting new TCP connections
        // 2. Close existing TCP connections gracefully
        // 3. Close serial port connection
        // 4. Wait for all tasks to complete
        
        info!("Application shutdown complete");
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logger
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    // Parse command line arguments
    let args = cli::parse_args();
    
    // Validate arguments
    cli::validate_args(&args)?;
    
    // Create configuration from arguments
    let config = Config::from_args(args)?;
    
    // Create and run application
    let mut app = Application::new(config).await?;
    app.run().await?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{SerialConfig, TcpConfig, LogConfig, LimitsConfig};
    use std::time::Duration;
    
    fn create_test_config() -> Config {
        Config {
            serial: SerialConfig {
                port: "COM1".to_string(),
                baud_rate: 9600,
                data_bits: serialport::DataBits::Eight,
                stop_bits: serialport::StopBits::One,
                parity: serialport::Parity::None,
                timeout: Duration::from_millis(1000),
            },
            tcp: TcpConfig {
                host: "127.0.0.1".to_string(),
                port: 8080,
                max_clients: 10,
                client_timeout: Duration::from_secs(300),
                buffer_size: 1024,
            },
            logging: LogConfig {
                level: log::LevelFilter::Info,
                file: None,
            },
            limits: LimitsConfig {
                max_message_size: 1024 * 1024, // 1MB
                max_queue_size: 1000,
            },
        }
    }
    
    #[tokio::test]
    async fn test_application_creation() {
        let config = create_test_config();
        let result = Application::new(config).await;
        
        // Note: This test might fail if serial port is not available
        // In a real test environment, we would use mock objects
        match result {
            Ok(_) => {
                // Application created successfully
            }
            Err(e) => {
                // Expected if no serial port is available
                println!("Application creation failed (expected in test): {}", e);
            }
        }
    }
}
